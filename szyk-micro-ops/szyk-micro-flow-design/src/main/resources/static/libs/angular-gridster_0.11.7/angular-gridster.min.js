/*
 * angular-gridster
 * http://manifestwebdesign.github.io/angular-gridster
 *
 * @version: 0.11.7
 * @license: MIT
 */
!function(a){"use strict";a.module("gridster",[]).constant("gridsterConfig",{columns:6,pushing:!0,floating:!0,swapping:!1,width:"auto",colWidth:"auto",rowHeight:"match",margins:[10,10],outerMargin:!0,isMobile:!1,mobileBreakPoint:600,mobileModeEnabled:!0,minColumns:1,minRows:1,maxRows:100,defaultSizeX:2,defaultSizeY:1,minSizeX:1,maxSizeX:null,minSizeY:1,maxSizeY:null,saveGridItemCalculatedHeightInMobile:!1,resizable:{enabled:!0,handles:["s","e","n","w","se","ne","sw","nw"]},draggable:{enabled:!0,scrollSensitivity:20,scrollSpeed:15}}).controller("GridsterCtrl",["gridsterConfig","$timeout",function(b,c){var d=this;a.extend(this,b),this.resizable=a.extend({},b.resizable||{}),this.draggable=a.extend({},b.draggable||{});var e=!1;this.layoutChanged=function(){e||(e=!0,c(function(){e=!1,d.loaded&&d.floatItemsUp(),d.updateHeight(d.movingItem?d.movingItem.sizeY:0)}))},this.grid=[],this.destroy=function(){this.grid&&(this.grid.length=0,this.grid=null)},this.setOptions=function(b){if(b)if(b=a.extend({},b),b.draggable&&(a.extend(this.draggable,b.draggable),delete b.draggable),b.resizable&&(a.extend(this.resizable,b.resizable),delete b.resizable),a.extend(this,b),this.margins&&2===this.margins.length)for(var c=0,d=this.margins.length;d>c;++c)this.margins[c]=parseInt(this.margins[c],10),isNaN(this.margins[c])&&(this.margins[c]=0);else this.margins=[0,0]},this.canItemOccupy=function(a,b,c){return b>-1&&c>-1&&a.sizeX+c<=this.columns&&a.sizeY+b<=this.maxRows},this.autoSetItemPosition=function(a){for(var b=0;b<this.maxRows;++b)for(var c=0;c<this.columns;++c){var d=this.getItems(b,c,a.sizeX,a.sizeY,a);if(0===d.length&&this.canItemOccupy(a,b,c))return void this.putItem(a,b,c)}throw new Error("Unable to place item!")},this.getItems=function(a,b,c,d,e){var f=[];c&&d||(c=d=1),!e||e instanceof Array||(e=[e]);for(var g=0;d>g;++g)for(var h=0;c>h;++h){var i=this.getItem(a+g,b+h,e);!i||e&&-1!==e.indexOf(i)||-1!==f.indexOf(i)||f.push(i)}return f},this.getBoundingBox=function(a){if(0===a.length)return null;if(1===a.length)return{row:a[0].row,col:a[0].col,sizeY:a[0].sizeY,sizeX:a[0].sizeX};for(var b=0,c=0,d=9999,e=9999,f=0,g=a.length;g>f;++f){var h=a[f];d=Math.min(h.row,d),e=Math.min(h.col,e),b=Math.max(h.row+h.sizeY,b),c=Math.max(h.col+h.sizeX,c)}return{row:d,col:e,sizeY:b-d,sizeX:c-e}},this.removeItem=function(a){for(var b=0,c=this.grid.length;c>b;++b){var d=this.grid[b];if(d){var e=d.indexOf(a);if(-1!==e){d[e]=null;break}}}this.layoutChanged()},this.getItem=function(a,b,c){!c||c instanceof Array||(c=[c]);for(var d=1;a>-1;){for(var e=1,f=b;f>-1;){var g=this.grid[a];if(g){var h=g[f];if(h&&(!c||-1===c.indexOf(h))&&h.sizeX>=e&&h.sizeY>=d)return h}++e,--f}--a,++d}return null},this.putItems=function(a){for(var b=0,c=a.length;c>b;++b)this.putItem(a[b])},this.putItem=function(a,b,c,d){if(("undefined"==typeof b||null===b)&&(b=a.row,c=a.col,"undefined"==typeof b||null===b))return void this.autoSetItemPosition(a);if(this.canItemOccupy(a,b,c)||(c=Math.min(this.columns-a.sizeX,Math.max(0,c)),b=Math.min(this.maxRows-a.sizeY,Math.max(0,b))),null!==a.oldRow&&"undefined"!=typeof a.oldRow){var e=a.oldRow===b&&a.oldColumn===c,f=this.grid[b]&&this.grid[b][c]===a;if(e&&f)return a.row=b,void(a.col=c);var g=this.grid[a.oldRow];g&&g[a.oldColumn]===a&&delete g[a.oldColumn]}a.oldRow=a.row=b,a.oldColumn=a.col=c,this.moveOverlappingItems(a,d),this.grid[b]||(this.grid[b]=[]),this.grid[b][c]=a,this.movingItem===a&&this.floatItemUp(a),this.layoutChanged()},this.swapItems=function(a,b){this.grid[a.row][a.col]=b,this.grid[b.row][b.col]=a;var c=a.row,d=a.col;a.row=b.row,a.col=b.col,b.row=c,b.col=d},this.moveOverlappingItems=function(a,b){b?-1===b.indexOf(a)&&(b=b.slice(0),b.push(a)):b=[a];var c=this.getItems(a.row,a.col,a.sizeX,a.sizeY,b);this.moveItemsDown(c,a.row+a.sizeY,b)},this.moveItemsDown=function(a,b,c){if(a&&0!==a.length){a.sort(function(a,b){return a.row-b.row}),c=c?c.slice(0):[];var d,e,f,g={};for(e=0,f=a.length;f>e;++e){d=a[e];var h=g[d.col];("undefined"==typeof h||d.row<h)&&(g[d.col]=d.row)}for(e=0,f=a.length;f>e;++e){d=a[e];var i=b-g[d.col];this.moveItemDown(d,d.row+i,c),c.push(d)}}},this.moveItemDown=function(a,b,c){if(!(a.row>=b)){for(;a.row<b;)++a.row,this.moveOverlappingItems(a,c);this.putItem(a,a.row,a.col,c)}},this.floatItemsUp=function(){if(this.floating!==!1)for(var a=0,b=this.grid.length;b>a;++a){var c=this.grid[a];if(c)for(var d=0,e=c.length;e>d;++d){var f=c[d];f&&this.floatItemUp(f)}}},this.floatItemUp=function(a){if(this.floating!==!1){for(var b=a.col,c=a.sizeY,d=a.sizeX,e=null,f=null,g=a.row-1;g>-1;){var h=this.getItems(g,b,d,c,a);if(0!==h.length)break;e=g,f=b,--g}null!==e&&this.putItem(a,e,f)}},this.updateHeight=function(a){var b=this.minRows;a=a||0;for(var c=this.grid.length;c>=0;--c){var d=this.grid[c];if(d)for(var e=0,f=d.length;f>e;++e)d[e]&&(b=Math.max(b,c+a+d[e].sizeY))}this.gridHeight=this.maxRows-b>0?Math.min(this.maxRows,b):Math.max(this.maxRows,b)},this.pixelsToRows=function(a,b){return b===!0?Math.ceil(a/this.curRowHeight):b===!1?Math.floor(a/this.curRowHeight):Math.round(a/this.curRowHeight)},this.pixelsToColumns=function(a,b){return b===!0?Math.ceil(a/this.curColWidth):b===!1?Math.floor(a/this.curColWidth):Math.round(a/this.curColWidth)},this.unifiedInput=function(a,b,c,d){var e,f,g={},h=function(a){if(Object.keys)return Object.keys(a).length;var b,c=0;for(b in a)++c;return c},i=function(a){for(var b=0,c=0,d=navigator.userAgent.match(/\bMSIE\b/),e=a;null!=e;e=e.offsetParent)d&&(!document.documentMode||document.documentMode<8)&&"relative"===e.currentStyle.position&&e.offsetParent&&"relative"===e.offsetParent.currentStyle.position&&e.offsetLeft===e.offsetParent.offsetLeft?c+=e.offsetTop:(b+=e.offsetLeft,c+=e.offsetTop);return{x:b,y:c}},j=i(a),k=function(e){if("mousemove"!==e.type||0!==h(g)){for(var f=!0,m=e.changedTouches?e.changedTouches:[e],n=0;n<m.length;++n){var o=m[n],p="undefined"!=typeof o.identifier?o.identifier:"undefined"!=typeof o.pointerId?o.pointerId:1;if("undefined"==typeof o.pageX)if(o.pageX=o.offsetX+j.x,o.pageY=o.offsetY+j.y,o.srcElement.offsetParent===a&&document.documentMode&&8===document.documentMode&&"mousedown"===o.type)o.pageX+=o.srcElement.offsetLeft,o.pageY+=o.srcElement.offsetTop;else if(o.srcElement!==a&&!document.documentMode||document.documentMode<8){for(var q=-2,r=-2,s=o.srcElement;null!==s;s=s.parentNode)q+=s.scrollLeft?s.scrollLeft:0,r+=s.scrollTop?s.scrollTop:0;o.pageX=o.clientX+q,o.pageY=o.clientY+r}var t=o.pageX,u=o.pageY;e.type.match(/(start|down)$/i)?(j=i(a),g[p]&&(d&&d({target:e.target,which:e.which,pointerId:p,pageX:t,pageY:u}),delete g[p]),b&&f&&(f=b({target:e.target,which:e.which,pointerId:p,pageX:t,pageY:u})),g[p]={x:t,y:u},a.msSetPointerCapture?a.msSetPointerCapture(p):"mousedown"===e.type&&1===h(g)&&(l?a.setCapture(!0):(document.addEventListener("mousemove",k,!1),document.addEventListener("mouseup",k,!1)))):e.type.match(/move$/i)?!g[p]||g[p].x===t&&g[p].y===u||(c&&f&&(f=c({target:e.target,which:e.which,pointerId:p,pageX:t,pageY:u})),g[p].x=t,g[p].y=u):g[p]&&e.type.match(/(up|end|cancel)$/i)&&(d&&f&&(f=d({target:e.target,which:e.which,pointerId:p,pageX:t,pageY:u})),delete g[p],a.msReleasePointerCapture?a.msReleasePointerCapture(p):"mouseup"===e.type&&0===h(g)&&(l?a.releaseCapture():(document.removeEventListener("mousemove",k,!1),document.removeEventListener("mouseup",k,!1))))}f&&(e.preventDefault&&e.preventDefault(),e.preventManipulation&&e.preventManipulation(),e.preventMouseEvent&&e.preventMouseEvent())}},l=!1;return this.enable=function(){window.navigator.msPointerEnabled?(a.addEventListener("MSPointerDown",k,!1),a.addEventListener("MSPointerMove",k,!1),a.addEventListener("MSPointerUp",k,!1),a.addEventListener("MSPointerCancel",k,!1),"undefined"!=typeof a.style.msContentZooming&&(e=a.style.msContentZooming,a.style.msContentZooming="none"),"undefined"!=typeof a.style.msTouchAction&&(f=a.style.msTouchAction,a.style.msTouchAction="none")):a.addEventListener?(a.addEventListener("touchstart",k,!1),a.addEventListener("touchmove",k,!1),a.addEventListener("touchend",k,!1),a.addEventListener("touchcancel",k,!1),a.addEventListener("mousedown",k,!1),a.setCapture&&!window.navigator.userAgent.match(/\bGecko\b/)&&(l=!0,a.addEventListener("mousemove",k,!1),a.addEventListener("mouseup",k,!1))):a.attachEvent&&a.setCapture&&(l=!0,a.attachEvent("onmousedown",function(){return k(window.event),window.event.returnValue=!1,!1}),a.attachEvent("onmousemove",function(){return k(window.event),window.event.returnValue=!1,!1}),a.attachEvent("onmouseup",function(){return k(window.event),window.event.returnValue=!1,!1}))},this.disable=function(){window.navigator.msPointerEnabled?(a.removeEventListener("MSPointerDown",k,!1),a.removeEventListener("MSPointerMove",k,!1),a.removeEventListener("MSPointerUp",k,!1),a.removeEventListener("MSPointerCancel",k,!1),e&&(a.style.msContentZooming=e),f&&(a.style.msTouchAction=f)):a.removeEventListener?(a.removeEventListener("touchstart",k,!1),a.removeEventListener("touchmove",k,!1),a.removeEventListener("touchend",k,!1),a.removeEventListener("touchcancel",k,!1),a.removeEventListener("mousedown",k,!1),a.setCapture&&!window.navigator.userAgent.match(/\bGecko\b/)&&(l=!0,a.removeEventListener("mousemove",k,!1),a.removeEventListener("mouseup",k,!1))):a.detachEvent&&a.setCapture&&(l=!0,a.detachEvent("onmousedown"),a.detachEvent("onmousemove"),a.detachEvent("onmouseup"))},this}}]).directive("gridster",["$timeout","$rootScope","$window",function(b,c,d){return{restrict:"EAC",transclude:!0,replace:!0,template:'<div ng-class="gridsterClass()"><div ng-style="previewStyle()" class="gridster-item gridster-preview-holder"></div><div class="gridster-content" ng-transclude></div></div>',controller:"GridsterCtrl",controllerAs:"gridster",scope:{config:"=?gridster"},compile:function(){return function(e,f,g,h){function i(){var a=f[0].offsetWidth||parseInt(f.css("width"),10);a&&a!==m&&!h.movingItem&&(m=a,h.loaded&&f.removeClass("gridster-loaded"),k(),h.loaded&&f.addClass("gridster-loaded"),e.$parent.$broadcast("gridster-resized",[a,f.offsetHeight]))}function j(){i(),b(function(){e.$apply()})}h.loaded=!1,e.gridsterClass=function(){return{gridster:!0,"gridster-desktop":!h.isMobile,"gridster-mobile":h.isMobile,"gridster-loaded":h.loaded}},e.previewStyle=function(){return h.movingItem?{display:"block",height:h.movingItem.sizeY*h.curRowHeight-h.margins[0]+"px",width:h.movingItem.sizeX*h.curColWidth-h.margins[1]+"px",top:h.movingItem.row*h.curRowHeight+(h.outerMargin?h.margins[0]:0)+"px",left:h.movingItem.col*h.curColWidth+(h.outerMargin?h.margins[1]:0)+"px"}:{display:"none"}};var k=function(){h.setOptions(e.config),h.curWidth="auto"===h.width?f[0].offsetWidth||parseInt(f.css("width"),10):h.width,h.curColWidth="auto"===h.colWidth?(h.curWidth+(h.outerMargin?-h.margins[1]:h.margins[1]))/h.columns:h.colWidth,h.curRowHeight=h.rowHeight,"string"==typeof h.rowHeight&&("match"===h.rowHeight?h.curRowHeight=Math.round(h.curColWidth):-1!==h.rowHeight.indexOf("*")?h.curRowHeight=Math.round(h.curColWidth*h.rowHeight.replace("*","").replace(" ","")):-1!==h.rowHeight.indexOf("/")&&(h.curRowHeight=Math.round(h.curColWidth/h.rowHeight.replace("/","").replace(" ","")))),h.isMobile=h.mobileModeEnabled&&h.curWidth<=h.mobileBreakPoint;for(var a=0,b=h.grid.length;b>a;++a){var c=h.grid[a];if(c)for(var d=0,g=c.length;g>d;++d)if(c[d]){var i=c[d];i.setElementPosition(),i.setElementSizeY(),i.setElementSizeX()}}l()};e.$watch("config",k,!0),e.$watch("config.draggable",function(){c.$broadcast("gridster-draggable-changed")},!0),e.$watch("config.resizable",function(){c.$broadcast("gridster-resizable-changed")},!0);var l=function(){f.css("height",h.gridHeight*h.curRowHeight+(h.outerMargin?h.margins[0]:-h.margins[0])+"px")};e.$watch("gridster.gridHeight",l),e.$watch("gridster.movingItem",function(){h.updateHeight(h.movingItem?h.movingItem.sizeY:0)});var m=f[0].offsetWidth||parseInt(f.css("width"),10);"function"==typeof f.resize&&f.resize(j);var n=a.element(d);n.on("resize",j),e.$watch(function(){return f[0].offsetWidth||parseInt(f.css("width"),10)},i),e.$on("$destroy",function(){h.destroy(),n.off("resize",j)}),b(function(){e.$watch("gridster.floating",function(){h.floatItemsUp()}),h.loaded=!0},100)}}}}]).controller("GridsterItemCtrl",function(){this.$element=null,this.gridster=null,this.row=null,this.col=null,this.sizeX=null,this.sizeY=null,this.minSizeX=0,this.minSizeY=0,this.maxSizeX=null,this.maxSizeY=null,this.init=function(a,b){this.$element=a,this.gridster=b,this.sizeX=b.defaultSizeX,this.sizeY=b.defaultSizeY},this.destroy=function(){this.gridster=null,this.$element=null},this.toJSON=function(){return{row:this.row,col:this.col,sizeY:this.sizeY,sizeX:this.sizeX}},this.isMoving=function(){return this.gridster.movingItem===this},this.setPosition=function(a,b){this.gridster.putItem(this,a,b),this.isMoving()||this.setElementPosition()},this.setSize=function(a,b,c){a=a.toUpperCase();var d="size"+a,e="Size"+a;if(""!==b){b=parseInt(b,10),(isNaN(b)||0===b)&&(b=this.gridster["default"+e]);var f="X"===a?this.gridster.columns:this.gridster.maxRows;this["max"+e]&&(f=Math.min(this["max"+e],f)),this.gridster["max"+e]&&(f=Math.min(this.gridster["max"+e],f)),"X"===a&&this.cols?f-=this.cols:"Y"===a&&this.rows&&(f-=this.rows);var g=0;this["min"+e]&&(g=Math.max(this["min"+e],g)),this.gridster["min"+e]&&(g=Math.max(this.gridster["min"+e],g)),b=Math.max(Math.min(b,f),g);var h=this[d]!==b||this["old"+e]&&this["old"+e]!==b;return this["old"+e]=this[d]=b,this.isMoving()||this["setElement"+e](),!c&&h&&(this.gridster.moveOverlappingItems(this),this.gridster.layoutChanged()),h}},this.setSizeY=function(a,b){return this.setSize("Y",a,b)},this.setSizeX=function(a,b){return this.setSize("X",a,b)},this.setElementPosition=function(){this.$element.css(this.gridster.isMobile?{marginLeft:this.gridster.margins[0]+"px",marginRight:this.gridster.margins[0]+"px",marginTop:this.gridster.margins[1]+"px",marginBottom:this.gridster.margins[1]+"px",top:"",left:""}:{margin:0,top:this.row*this.gridster.curRowHeight+(this.gridster.outerMargin?this.gridster.margins[0]:0)+"px",left:this.col*this.gridster.curColWidth+(this.gridster.outerMargin?this.gridster.margins[1]:0)+"px"})},this.setElementSizeY=function(){this.gridster.isMobile&&!this.gridster.saveGridItemCalculatedHeightInMobile?this.$element.css("height",""):this.$element.css("height",this.sizeY*this.gridster.curRowHeight-this.gridster.margins[0]+"px")},this.setElementSizeX=function(){this.gridster.isMobile?this.$element.css("width",""):this.$element.css("width",this.sizeX*this.gridster.curColWidth-this.gridster.margins[1]+"px")},this.getElementSizeX=function(){return this.sizeX*this.gridster.curColWidth-this.gridster.margins[1]},this.getElementSizeY=function(){return this.sizeY*this.gridster.curRowHeight-this.gridster.margins[0]}}).factory("GridsterDraggable",["$document","$timeout","$window",function(b,c,d){function e(e,f,g,h,i){function j(b){if(-1!==G.indexOf(b.target.nodeName.toLowerCase()))return!1;if(a.element(b.target).hasClass("gridster-item-resizable-handler"))return!1;if(a.element(b.target).attr("onclick")||a.element(b.target).attr("ng-click"))return!1;switch(b.which){case 1:break;case 2:case 3:return}return y=b.pageX,z=b.pageY,p=parseInt(e.css("left"),10),q=parseInt(e.css("top"),10),r=e[0].offsetWidth,s=e[0].offsetHeight,t=h.col,u=h.row,m(b),!0}function k(a){if(!e.hasClass("gridster-item-moving")||e.hasClass("gridster-item-resizing"))return!1;var b=g.curWidth-1;w=a.pageX,x=a.pageY;var c=w-y+A,d=x-z+B;A=B=0,y=w,z=x;var f=c,h=d;return E>p+f?(c=E-p,A=f-c):p+r+f>b&&(c=b-p-r,A=f-c),C>q+h?(d=C-q,B=h-d):q+s+h>D&&(d=D-q-s,B=h-d),p+=c,q+=d,e.css({top:q+"px",left:p+"px"}),n(a),!0}function l(a){return!e.hasClass("gridster-item-moving")||e.hasClass("gridster-item-resizing")?!1:(A=B=0,o(a),!0)}function m(a){e.addClass("gridster-item-moving"),g.movingItem=h,g.updateHeight(h.sizeY),f.$apply(function(){g.draggable&&g.draggable.start&&g.draggable.start(a,e,i)})}function n(a){var b=h.row,c=h.col,j=g.draggable&&g.draggable.drag,k=g.draggable.scrollSensitivity,l=g.draggable.scrollSpeed,m=g.pixelsToRows(q),n=g.pixelsToColumns(p),o=g.getItems(m,n,h.sizeX,h.sizeY,h),r=0!==o.length;if(g.swapping===!0&&r){var s=g.getBoundingBox(o),t=s.sizeX===h.sizeX&&s.sizeY===h.sizeY,u=s.row===m,v=s.col===n,w=u&&v,x=u||v;if(t&&1===o.length){if(w)g.swapItems(h,o[0]);else if(x)return}else if(s.sizeX<=h.sizeX&&s.sizeY<=h.sizeY&&x)for(var y=h.row<=m?h.row:m+h.sizeY,z=h.col<=n?h.col:n+h.sizeX,A=y-s.row,B=z-s.col,C=0,D=o.length;D>C;++C){var E=o[C],G=g.getItems(E.row+A,E.col+B,E.sizeX,E.sizeY,h);0===G.length&&g.putItem(E,E.row+A,E.col+B)}}g.pushing===!1&&r||(h.row=m,h.col=n),a.pageY-F.body.scrollTop<k?F.body.scrollTop=F.body.scrollTop-l:d.innerHeight-(a.pageY-F.body.scrollTop)<k&&(F.body.scrollTop=F.body.scrollTop+l),a.pageX-F.body.scrollLeft<k?F.body.scrollLeft=F.body.scrollLeft-l:d.innerWidth-(a.pageX-F.body.scrollLeft)<k&&(F.body.scrollLeft=F.body.scrollLeft+l),(j||b!==h.row||c!==h.col)&&f.$apply(function(){j&&g.draggable.drag(a,e,i)})}function o(a){e.removeClass("gridster-item-moving");var b=g.pixelsToRows(q),c=g.pixelsToColumns(p);(g.pushing!==!1||0===g.getItems(b,c,h.sizeX,h.sizeY,h).length)&&(h.row=b,h.col=c),g.movingItem=null,h.setPosition(h.row,h.col),f.$apply(function(){g.draggable&&g.draggable.stop&&g.draggable.stop(a,e,i)})}var p,q,r,s,t,u,v,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=9999,E=0,F=b[0],G=["select","input","textarea","button"],H=!1,I=null;this.enable=function(){var b=this;c(function(){b.disable(),g.draggable&&g.draggable.handle?(I=a.element(e[0].querySelector(g.draggable.handle)),0===I.length&&(I=e)):I=e,v=new g.unifiedInput(I[0],j,k,l),v.enable(),H=!0})},this.disable=function(){H&&(v.disable(),v=void 0,H=!1)},this.toggle=function(a){a?this.enable():this.disable()},this.destroy=function(){this.disable()}}return e}]).factory("GridsterResizable",[function(){function b(b,c,d,e,f){function g(g){function h(a){switch(a.which){case 1:break;case 2:case 3:return}return t=d.draggable.enabled,t&&(d.draggable.enabled=!1,c.$broadcast("gridster-draggable-changed")),y=a.pageX,z=a.pageY,n=parseInt(b.css("left"),10),o=parseInt(b.css("top"),10),p=b[0].offsetWidth,q=b[0].offsetHeight,r=e.sizeX,s=e.sizeY,i(a),!0}function i(a){b.addClass("gridster-item-moving"),b.addClass("gridster-item-resizing"),d.movingItem=e,e.setElementSizeX(),e.setElementSizeY(),e.setElementPosition(),d.updateHeight(1),c.$apply(function(){d.resizable&&d.resizable.start&&d.resizable.start(a,b,f)})}function j(a){var c=d.curWidth-1;w=a.pageX,x=a.pageY;var e=w-y+A,f=x-z+B;A=B=0,y=w,z=x;var g=f,h=e;return v.indexOf("n")>=0&&(q-g<F()?(f=q-F(),B=g-f):C>o+g&&(f=C-o,B=g-f),o+=f,q-=f),v.indexOf("s")>=0&&(q+g<F()?(f=F()-q,B=g-f):o+q+g>D&&(f=D-o-q,B=g-f),q+=f),v.indexOf("w")>=0&&(p-h<G()?(e=p-G(),A=h-e):E>n+h&&(e=E-n,A=h-e),n+=e,p-=e),v.indexOf("e")>=0&&(p+h<G()?(e=G()-p,A=h-e):n+p+h>c&&(e=c-n-p,A=h-e),p+=e),b.css({top:o+"px",left:n+"px",width:p+"px",height:q+"px"}),l(a),!0}function k(a){return d.draggable.enabled!==t&&(d.draggable.enabled=t,c.$broadcast("gridster-draggable-changed")),A=B=0,m(a),!0}function l(a){var h=e.row,i=e.col,j=e.sizeX,k=e.sizeY,l=d.resizable&&d.resizable.resize,m=e.col;-1!==["w","nw","sw"].indexOf(g)&&(m=d.pixelsToColumns(n,!1));var r=e.row;-1!==["n","ne","nw"].indexOf(g)&&(r=d.pixelsToRows(o,!1));var s=e.sizeX;-1===["n","s"].indexOf(g)&&(s=d.pixelsToColumns(p,!0));var t=e.sizeY;-1===["e","w"].indexOf(g)&&(t=d.pixelsToRows(q,!0)),(d.pushing!==!1||0===d.getItems(r,m,s,t,e).length)&&(e.row=r,e.col=m,e.sizeX=s,e.sizeY=t);var u=e.row!==h||e.col!==i||e.sizeX!==j||e.sizeY!==k;(l||u)&&c.$apply(function(){l&&d.resizable.resize(a,b,f)})}function m(a){b.removeClass("gridster-item-moving"),b.removeClass("gridster-item-resizing"),d.movingItem=null,e.setPosition(e.row,e.col),e.setSizeY(e.sizeY),e.setSizeX(e.sizeX),c.$apply(function(){d.resizable&&d.resizable.stop&&d.resizable.stop(a,b,f)})}var n,o,p,q,r,s,t,u,v=g,w=0,x=0,y=0,z=0,A=0,B=0,C=0,D=9999,E=0,F=function(){return d.curRowHeight-d.margins[0]},G=function(){return d.curColWidth-d.margins[1]},H=null;this.enable=function(){H||(H=a.element('<div class="gridster-item-resizable-handler handle-'+v+'"></div>'),b.append(H)),u=new d.unifiedInput(H[0],h,j,k),u.enable()},this.disable=function(){H&&(H.remove(),H=null),u.disable(),u=void 0},this.destroy=function(){this.disable()}}var h=[],i=d.resizable.handles;"string"==typeof i&&(i=d.resizable.handles.split(","));for(var j=!1,k=0,l=i.length;l>k;k++)h.push(new g(i[k]));this.enable=function(){if(!j){for(var a=0,b=h.length;b>a;a++)h[a].enable();j=!0}},this.disable=function(){if(j){for(var a=0,b=h.length;b>a;a++)h[a].disable();j=!1}},this.toggle=function(a){a?this.enable():this.disable()},this.destroy=function(){for(var a=0,b=h.length;b>a;a++)h[a].destroy()}}return b}]).directive("gridsterItem",["$parse","GridsterDraggable","GridsterResizable",function(a,b,c){return{restrict:"EA",controller:"GridsterItemCtrl",require:["^gridster","gridsterItem"],link:function(d,e,f,g){function h(){n.setPosition(n.row,n.col),q.row&&q.row.assign&&q.row.assign(d,n.row),q.col&&q.col.assign&&q.col.assign(d,n.col)}function i(){var a=n.setSizeX(n.sizeX,!0);a&&q.sizeX&&q.sizeX.assign&&q.sizeX.assign(d,n.sizeX);var b=n.setSizeY(n.sizeY,!0);b&&q.sizeY&&q.sizeY.assign&&q.sizeY.assign(d,n.sizeY),(a||b)&&(n.gridster.moveOverlappingItems(n),m.layoutChanged())}function j(){var a=document.createElement("div"),b={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(var c in b)if(void 0!==a.style[c])return b[c]}var k,l=f.gridsterItem,m=g[0],n=g[1];if(l){var o=a(l);k=o(d)||{},!k&&o.assign&&(k={row:n.row,col:n.col,sizeX:n.sizeX,sizeY:n.sizeY,minSizeX:0,minSizeY:0,maxSizeX:null,maxSizeY:null},o.assign(d,k))}else k=f;n.init(e,m),e.addClass("gridster-item");for(var p=["minSizeX","maxSizeX","minSizeY","maxSizeY","sizeX","sizeY","row","col"],q={},r=function(b){var c;if("string"==typeof k[b])c=k[b];else if("string"==typeof k[b.toLowerCase()])c=k[b.toLowerCase()];else{if(!l)return;c=a(l+"."+b)}q[b]=a(c),d.$watch(c,function(a){a=parseInt(a,10),isNaN(a)||(n[b]=a)});var e=q[b](d);"number"==typeof e&&(n[b]=e)},s=0,t=p.length;t>s;++s)r(p[s]);d.$broadcast("gridster-item-initialized",[n.sizeY,n.sizeX,n.getElementSizeY(),n.getElementSizeX()]),d.$watch(function(){return n.row+","+n.col},h),d.$watch(function(){return n.sizeY+","+n.sizeX+"|"+n.minSizeX+","+n.maxSizeX+","+n.minSizeY+","+n.maxSizeY},i);var u=new b(e,d,m,n,k),v=new c(e,d,m,n,k);return d.$on("gridster-draggable-changed",function(){u.toggle(!m.isMobile&&m.draggable&&m.draggable.enabled)}),d.$on("gridster-resizable-changed",function(){v.toggle(!m.isMobile&&m.resizable&&m.resizable.enabled)}),d.$on("gridster-resized",function(){v.toggle(!m.isMobile&&m.resizable&&m.resizable.enabled)}),d.$watch(function(){return m.isMobile},function(){v.toggle(!m.isMobile&&m.resizable&&m.resizable.enabled),u.toggle(!m.isMobile&&m.draggable&&m.draggable.enabled)}),e.on(j(),function(){d.$apply(function(){d.$broadcast("gridster-item-transition-end")})}),d.$on("$destroy",function(){try{v.destroy(),u.destroy()}catch(a){}try{m.removeItem(n)}catch(a){}try{n.destroy()}catch(a){}})}}}])}(angular);