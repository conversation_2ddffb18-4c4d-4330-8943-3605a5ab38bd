/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 机理类型枚举类
 *
 * <AUTHOR>
 * @date 2024/04/15 15:56
 **/
@Getter
@AllArgsConstructor
public enum ModelTypeEnum {

	/**
	 * 不平衡
	 */
	IMBALANCE(1, "不平衡"),

	/**
	 * 不对中
	 */
	MISALIGNMENT(2, "不对中"),

	/**
	 * 结构松动
	 */
	STRUCTURE_LOOSENESS(3, "结构松动"),

	/**
	 * 轴承故障
	 */
	BEARINBG_FAULT(4, "轴承故障"),

	/**
	 * 齿轮故障
	 */
	GEAR_FAULT(5, "齿轮故障"),

	/**
	 * 叶轮故障
	 */
	VANE_FAULT(6, "叶轮故障"),

	/**
	 * 摩擦故障
	 */
	FRICTION_FAULT(7, "摩擦故障"),

	/**
	 * 润滑不良
	 */
	POOR_LUBRICATION(8, "润滑不良"),

	/**
	 * 暂不支持 - 防止switch空指针！
	 */
	UNSUPPORTED(9999, "暂不支持"),
	;

	final Integer code;
	final String name;

	public static ModelTypeEnum getByCode(Integer code) {
		for (ModelTypeEnum value : ModelTypeEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return UNSUPPORTED;
	}

}
