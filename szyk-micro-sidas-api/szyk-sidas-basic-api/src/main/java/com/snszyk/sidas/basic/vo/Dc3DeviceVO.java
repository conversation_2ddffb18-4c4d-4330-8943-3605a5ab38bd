package com.snszyk.sidas.basic.vo;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.utils.Func;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求参数
 * @description:
 * @ClassName: Dc3DeviceVO
 * @author: wangmh
 * @create: 2022-10-13 13:53
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Dc3DeviceVO {

	/**
	 * 分页信息
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Pages page;
	/**
	 * 设备名称
	 */
	private String name;

	/**
	 * 是否启用
	 */
	private Boolean enable;

	public static Dc3DeviceVO create(String name, Query query){
		Pages page = new Pages();
		if (query.getCurrent() != null){
			page.setCurrent(query.getCurrent());
		}
		if (query.getSize() != null){
			page.setSize(query.getSize());
		}
		if (Func.isNotEmpty(query.getAscs())){
			page.getOrders().addAll(OrderItem.ascs(query.getAscs()));
		}
		if (Func.isNotEmpty(query.getDescs())){
			page.getOrders().addAll(OrderItem.descs(query.getDescs()));
		}
		return new Dc3DeviceVO(page,name,true);
	}
}
