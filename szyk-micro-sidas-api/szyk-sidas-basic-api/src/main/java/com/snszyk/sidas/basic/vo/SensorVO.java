/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 传感器 视图实体类
 *
 * <AUTHOR>
 * @since 2022-11-03
 */
@Data
@ApiModel(value = "SensorVO对象", description = "SensorVO对象")
public class SensorVO implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 传感器id
	 */
	@ApiModelProperty(value = "传感器id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 采集站名称
	 */
	@ApiModelProperty(value = "采集站名称")
	private String name;

	/**
	 * 采集站ip
	 */
	@ApiModelProperty(value = "采集站ip")
	private String ip;

	/**
	 * 传感器名称
	 */
	@ApiModelProperty(value = "传感器名称")
	private String pointName;

	/**
	 * 传感器类型
	 */
	@ApiModelProperty(value = "传感器类型（字典：sensor_category）")
	private String category;

	/**
	 * 传感器类型
	 */
	@ApiModelProperty(value = "传感器类型（字典：sensor_category）")
	private String categoryName;

}
