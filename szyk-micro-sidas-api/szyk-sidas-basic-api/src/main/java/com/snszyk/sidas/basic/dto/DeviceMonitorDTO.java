package com.snszyk.sidas.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 设备测点
 * @ClassName: DevicePoint
 * @author: wangmh
 * @create: 2022-12-20 16:20
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "设备测点实体")
public class DeviceMonitorDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("测点id")
	private Long monitorId;

	@ApiModelProperty(value = "测点名称")
	private String monitorName;

	@ApiModelProperty("传感器数据列表")
	private List<SensorData> sensorDataList;

	/**
	 * 传感器数据
	 */
	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	@ApiModel("设备测点传感器数据")
	public static class SensorData implements Serializable {

		private static final long serialVersionUID = 1L;

		@ApiModelProperty(value = "传感器数据值")
		private BigDecimal value;

		@ApiModelProperty(value = "单位")
		private String unit;

	}
}
