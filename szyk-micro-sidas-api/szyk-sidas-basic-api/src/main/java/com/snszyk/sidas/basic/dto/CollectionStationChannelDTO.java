/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.sidas.basic.dto;

import com.snszyk.sidas.basic.entity.CollectionStationChannel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class CollectionStationChannelDTO extends CollectionStationChannel {

	private static final long serialVersionUID = 1L;

	/**
	 * 传感器路径
	 */
	@ApiModelProperty("传感器路径")
	private String pathName;

	/**
	 * 传感器名称
	 */
	@ApiModelProperty("传感器名称")
	private String sensorName;

	/**
	 * 电量传感器参数id
	 */
	@ApiModelProperty(value = "电量传感器参数id")
	private Long spowerSensorInstanceParamId;

	/**
	 * 电量
	 */
	@ApiModelProperty(value = "电量")
	private String electricity;

	/**
	 * 传感器在线状态参数id
	 */
	@ApiModelProperty(value = "传感器在线状态参数id")
	private Long onlineSensorInstanceParamId;

	/**
	 * 是否无线：0-有线；1-无线
	 */
	@ApiModelProperty(value = "是否无线：0-有线；1-无线")
	private String isWireless;

}
