package com.snszyk.sidas.diagnosis.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 设备报警概况
 * @ClassName: AlarmDeviceDetail
 * @author: wangmh
 * @create: 2022-12-16 15:47
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlarmDeviceDetailDTO {

	@ApiModelProperty(value = "地点")
	private List<String> name;

	@ApiModelProperty(value = "正常")
	private List<Integer> normal;

	@ApiModelProperty(value = "异常")
	private List<Integer> exception;
}
