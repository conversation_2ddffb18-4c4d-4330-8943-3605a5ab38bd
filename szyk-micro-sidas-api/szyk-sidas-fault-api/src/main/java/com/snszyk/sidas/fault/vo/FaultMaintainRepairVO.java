/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.fault.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 故障维护维修视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Data
@ApiModel(value = "FaultMaintenanceRepairVO对象", description = "故障维护维修")
public class FaultMaintainRepairVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 维护人id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维护人id")
	private Long maintainUser;

	/**
	 * 维护人姓名
	 */
	@ApiModelProperty(value = "维护人姓名")
	private String maintainUserName;

	/**
	 * 检维护类型（字典：maintainType）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检维护类型（字典：maintainType）")
	private Integer maintainType;

	/**
	 * 检维护类型
	 */
	@ApiModelProperty(value = "检维护类型")
	private String maintainTypeName;

	/**
	 * 检维护开始时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "检维护开始时间")
	private Date overhaulStartTime;

	/**
	 * 检维护结束时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "检维护结束时间")
	private Date overhaulEndTime;

	/**
	 * 更换部件
	 */
	@ApiModelProperty(value = "更换部件")
	private List<FaultPositionVO> replacePart;

	/**
	 * 检维护费用
	 */
	@ApiModelProperty(value = "检维护费用")
	private BigDecimal overhaulCost;

	/**
	 * 检维护内容
	 */
	@ApiModelProperty(value = "检维护内容")
	private String overhaulContent;

	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;

	/**
	 * 上传图片
	 */
	@ApiModelProperty(value = "上传图片")
	private String attach;

	/**
	 * 接收人
	 */
	@ApiModelProperty(value = "接收人")
	private String receiver;

	/**
	 * 单据提交时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "单据提交时间")
	private Date submitTime;

}
