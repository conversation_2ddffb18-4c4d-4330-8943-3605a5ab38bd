/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.smart.vo;

import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 月度诊断报告视图对象
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SmartMonthlyReportVO对象", description = "月度诊断报告")
public class SmartMonthlyReportVO extends SmartMonthlyReport {

	private static final long serialVersionUID = 1L;

	/**
	 * 数据开始时间
	 */
	@ApiModelProperty(value = "查询-开始时间")
	private String startTime;

	/**
	 * 数据结束时间
	 */
	@ApiModelProperty(value = "查询-结束时间")
	private String endTime;

}
