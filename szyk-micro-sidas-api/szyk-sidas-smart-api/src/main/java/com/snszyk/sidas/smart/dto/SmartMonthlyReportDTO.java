/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.smart.dto;

import com.snszyk.resource.entity.Attach;
import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 月度诊断报告数据传输对象
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SmartMonthlyReportDTO对象", description = "月度诊断报告")
public class SmartMonthlyReportDTO extends SmartMonthlyReport {

	private static final long serialVersionUID = 1L;

	/**
	 * 附件文件名
	 */
	@ApiModelProperty(value = "附件文件名")
	private Attach file;

}
