package com.snszyk.sidas.smart.model;

import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WorkflowRequest extends BaseRequest {

    @Override
    public String buildRequestBody() {
        JSONObject body = new JSONObject();
        body.set("user", user);
		body.set("response_mode", responseMode);
		body.set("inputs", inputs);
        return body.toString();
    }
}
