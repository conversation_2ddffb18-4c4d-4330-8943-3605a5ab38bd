package com.snszyk.sidas.smart.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Data
@Accessors(chain = true)
public abstract class BaseRequest {
    protected String user;
    protected String baseUrl;
    protected String uri;
    protected String authorization;
    protected Map<String, Object> inputs = new HashMap<>();
    protected String responseMode = "streaming";

    public abstract String buildRequestBody();
}
