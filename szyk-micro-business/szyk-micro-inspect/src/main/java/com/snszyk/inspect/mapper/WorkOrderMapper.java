/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.inspect.dto.StatisticsDTO;
import com.snszyk.inspect.dto.WorkOrderDTO;
import com.snszyk.inspect.entity.WorkOrder;
import com.snszyk.inspect.vo.StatisticsSearchVO;
import com.snszyk.inspect.vo.WorkOrderVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备点检工单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
public interface WorkOrderMapper extends BaseMapper<WorkOrder> {

	/**
	 * 根据任务id查询点检工单
	 *
	 * @param taskId
	 * @return
	 */
	List<WorkOrderDTO> selectOrderList(Long taskId);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param orderVO
	 * @return
	 */
	List<WorkOrderDTO> page(IPage page, @Param("order") WorkOrderVO orderVO);

	/**
	 * 异常管理分页
	 *
	 * @param page
	 * @param orderVO
	 * @return
	 */
	List<WorkOrderDTO> abnormalPage(IPage page, @Param("order") WorkOrderVO orderVO);

	/**
	 * 设备管理分页
	 *
	 * @param page
	 * @param orderVO
	 * @return
	 */
	List<WorkOrderDTO> equipmentPage(IPage page, @Param("order") WorkOrderVO orderVO);

	/**
	 * 异常设备排名
	 *
	 * @param search
	 * @return
	 */
	List<StatisticsDTO> abnormalEquipmentRank(@Param("search") StatisticsSearchVO search);

	/**
	 * 异常设备数
	 *
	 * @return
	 */
	Integer abnormalEquipmentCount();

}

