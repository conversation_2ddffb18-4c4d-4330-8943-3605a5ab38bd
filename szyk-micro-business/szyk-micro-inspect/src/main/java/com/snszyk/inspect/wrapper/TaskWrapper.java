/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.inspect.entity.Task;
import com.snszyk.inspect.vo.TaskVO;

import java.util.Objects;

/**
 * 设备点检任务包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-06-15
 */
public class TaskWrapper extends BaseEntityWrapper<Task, TaskVO> {

	public static TaskWrapper build() {
		return new TaskWrapper();
 	}

	@Override
	public TaskVO entityVO(Task task) {
		TaskVO taskVO = Objects.requireNonNull(BeanUtil.copy(task, TaskVO.class));

		//User createUser = UserCache.getUser(task.getCreateUser());
		//User updateUser = UserCache.getUser(task.getUpdateUser());
		//taskVO.setCreateUserName(createUser.getName());
		//taskVO.setUpdateUserName(updateUser.getName());

		return taskVO;
	}

}
