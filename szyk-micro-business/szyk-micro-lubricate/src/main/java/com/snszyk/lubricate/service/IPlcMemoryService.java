/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.lubricate.entity.PlcMemory;

import java.util.concurrent.LinkedBlockingQueue;

/**
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IPlcMemoryService extends IService<PlcMemory> {

	/**
	 * 更新或删除操作队列，当更新完成或删除完成时，插入一条数据
	 */
	LinkedBlockingQueue<Boolean> UPDATE_OR_DELETE = new LinkedBlockingQueue<>(1);

	LinkedBlockingQueue<String> MEMORY_DATA_UPDATE = new LinkedBlockingQueue<>(5000);


}
