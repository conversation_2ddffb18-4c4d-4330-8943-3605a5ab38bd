/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.lubricate.entity.PlcConfig;
import com.snszyk.lubricate.entity.PlcMemory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * PLC内存配置信息
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface PlcMemoryMapper extends BaseMapper<PlcMemory> {



	List<PlcMemory> getList(@Param("configId") Long configId);

}
