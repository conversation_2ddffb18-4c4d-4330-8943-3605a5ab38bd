/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.lubricate;

import com.snszyk.core.cloud.feign.EnableSzykFeign;
import com.snszyk.core.launch.SzykApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;

/**
 * 润滑模块启动器
 * <AUTHOR>
 */
@EnableSzykFeign
@SpringCloudApplication
@EnableScheduling
public class LubricateApplication {


	public static void main(String[] args) {
		SzykApplication.run("szyk-micro-lubricate", LubricateApplication.class, args);
	}

}

