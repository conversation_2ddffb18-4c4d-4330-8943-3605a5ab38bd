/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("szyk_upload_task_attach")
@ApiModel(value = "UploadTaskAttach对象", description = "UploadTaskAttach对象")
public class UploadTaskAttach implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 分片上传任务id
	 */
	@ApiModelProperty(value = "分片上传任务id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long taskId;

	/**
	 * 附件表id
	 */
	@ApiModelProperty(value = "附件表id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long attachId;

	/**
	 * 文件名称
	 */
	@ApiModelProperty(value = "文件名称")
	private String fileName;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private Date createTime;

}
