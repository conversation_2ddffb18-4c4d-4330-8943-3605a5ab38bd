/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 润滑场景表实体类
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@TableName("lubricate_scene")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Scene对象", description = "润滑场景表")
public class Scene extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 图片
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "图片")
	private Long image;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
