/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警等级枚举类
 *
 * <AUTHOR>
 * @date 2022/12/13 15:10
 **/
@Getter
@AllArgsConstructor
public enum AlarmLevelEnum {

	/**
	 * 正常
	 */
	NORMAL(0, "正常"),
	/**
	 * 1级
	 */
	LEVEL_ONE(1, "1级"),
	/**
	 * 2级
	 */
	LEVEL_TWO(2, "2级"),
	/**
	 * 3级
	 */
	LEVEL_THREE(3, "3级"),
	/**
	 * 4级
	 */
	LEVEL_FOUR(4, "4级"),

	/**
	 * 未知
	 */
	NOT_SUPPORTED(Integer.MAX_VALUE, "未知"),
	;

	final Integer code;
	final String name;

	public static AlarmLevelEnum getByCode(Integer code){
		for (AlarmLevelEnum value : AlarmLevelEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return NOT_SUPPORTED;
	}

}
