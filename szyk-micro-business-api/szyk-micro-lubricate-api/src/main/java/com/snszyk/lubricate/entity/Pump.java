/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.lubricate.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 润滑泵表实体类
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@Accessors(chain = true)
@TableName("lubricate_pump")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Pump对象", description = "润滑泵表")
public class Pump extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 系统ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "系统ID")
	private Long systemId;
	/**
	 * 关联泵ID
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "关联泵ID")
	private Long pumpId;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private Integer code;
	/**
	 * 类型（0：主泵；1：备泵）
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "类型（0：主泵；1：备泵）")
	private Integer type;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;


}
