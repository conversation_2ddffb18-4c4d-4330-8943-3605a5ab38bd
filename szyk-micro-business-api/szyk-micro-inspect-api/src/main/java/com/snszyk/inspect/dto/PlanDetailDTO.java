/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.inspect.dto;

import com.snszyk.inspect.entity.PlanDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备点检计划明细数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PlanDetailDTO extends PlanDetail {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备路径
	 */
	@ApiModelProperty(value = "设备路径")
	private String equipmentPath;

}
