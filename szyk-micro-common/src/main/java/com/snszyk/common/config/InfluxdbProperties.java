package com.snszyk.common.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "szyk.influxdb")
public class InfluxdbProperties {

	private String url;
	private String token;
	private String org = "szyk";
	private String bucket = "sidas";
	private String username = "root";
	/**
	 * 默认未启动状态
	 */
	private Boolean enable = false;

}
