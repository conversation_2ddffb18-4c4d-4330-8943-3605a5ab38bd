package com.snszyk.common.config;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@EnableConfigurationProperties(InfluxdbProperties.class)
public class InfluxdbConfig implements Serializable {

	@Resource
	private InfluxdbProperties influxdbProperties;

	// 自定义 OkHttpClient 配置超时时间
	OkHttpClient okHttpClient = new OkHttpClient.Builder()
		.connectTimeout(3600, TimeUnit.SECONDS)  // 连接超时（默认无限制）
		.readTimeout(3600, TimeUnit.SECONDS)    // 读取超时（默认无限制）
		.writeTimeout(3600, TimeUnit.SECONDS)    // 写入超时（默认无限制）
		.build();

	@Bean
	@ConditionalOnProperty(prefix = "szyk.influxdb", name = { "enable" }, havingValue = "true", matchIfMissing = false)
	public InfluxDBClient influxDBClient() {
		log.debug("当前URL:{}", this.influxdbProperties.getUrl());
		log.debug("当前Token:{}", this.influxdbProperties.getToken());
		log.debug("当前Org:{}", this.influxdbProperties.getOrg());
		log.debug("当前Bucket:{}", this.influxdbProperties.getBucket());
		InfluxDBClientOptions options = InfluxDBClientOptions.builder()
			.url(this.influxdbProperties.getUrl())
			.authenticateToken(this.influxdbProperties.getToken().toCharArray())
			.org(influxdbProperties.getOrg())
			.bucket(influxdbProperties.getBucket())
			.okHttpClient(okHttpClient.newBuilder())
			.build();
		log.debug("当前options:{}", options);
		return InfluxDBClientFactory.create(options);
	}

}
