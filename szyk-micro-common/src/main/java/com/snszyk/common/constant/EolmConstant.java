package com.snszyk.common.constant;

/**
 * 设备诊断相关常量
 * <AUTHOR>
 */
public interface EolmConstant {

	/**
	 * 地点相关
	 */
	interface Device {
		/**
		 * 顶级地点的level
		 */
		Integer TOP_LEVEL = 0;
	}

	/**
	 * 时间相关
	 */
	interface Time {
		/**
		 * 时区
		 */
		String TIMEZONE = "GMT+8";

		/**
		 * 时间格式化
		 */
		String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
		String COMPLETE_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
	}

	/**
	 * RabbitMQ 相关
	 */
	interface Rabbit {
		// Arguments
		String MESSAGE_TTL = "x-message-ttl";

		// 通用指标 -> 报警门限新增队列
		String QUEUE_ALARM_THRESHOLD_ADD = "queue.alarm.threshold.add";
		// 通用指标 -> 报警门限新增路由
		String ROUTING_ALARM_THRESHOLD_ADD = "routing.alarm.threshold.add";
		// 采样指标 -> 报警门限新增队列
		String QUEUE_SAMPLING_THRESHOLD_ADD = "queue.sampling.threshold.add";
		// 采样指标 -> 报警门限新增路由
		String ROUTING_SAMPLING_THRESHOLD_ADD = "routing.sampling.threshold.add";
		// 报警门限删除队列
		String QUEUE_ALARM_THRESHOLD_REMOVE = "queue.alarm.threshold.remove";
		// 报警门限删除路由
		String ROUTING_ALARM_THRESHOLD_REMOVE = "routing.alarm.threshold.remove";
		// 报警门限更新队列
		String QUEUE_ALARM_THRESHOLD_UPDATE = "queue.alarm.threshold.update";
		// 报警门限更新路由
		String ROUTING_ALARM_THRESHOLD_UPDATE = "routing.alarm.threshold.update";
		// 报警门限交换机
		String EXCHANGE_ALARM_THRESHOLD = "exchange.alarm.threshold";

		// 生产结构树节点名称（地点、设备、测点）修改交换机
		String EXCHANGE_BASIC_TREE_NAME_UPDATE = "exchange.basic.tree.name.update";
		// 生产结构树节点名称（地点、设备、测点）修改队列 - basic服务
		String QUEUE_BASIC_TREE_NAME_UPDATE_BASIC = "queue.basic.tree.name.update.basic";
		// 生产结构树节点名称（地点、设备、测点）修改队列 - diagnosis服务
		String QUEUE_BASIC_TREE_NAME_UPDATE_DIAGNOSIS = "queue.basic.tree.name.update.diagnosis";
		// 生产结构树节点名称（地点、设备、测点）修改队列 - fault服务
		String QUEUE_BASIC_TREE_NAME_UPDATE_FAULT = "queue.basic.tree.name.update.fault";

		// Sub/Pub Value - DC3供其他系统订阅的PointValue交换机
		String FANOUT_EXCHANGE_VALUE = "dc3.fanout.exchange.value";
		String QUEUE_POINT_VALUE_EOLM = "dc3.queue.value.eolm";

		//报警交换机 - 通过routingKey绑定具体的队列
		String DIRECT_EXCHANGE_ALARM = "direct.exchange.alarm";
		//报警检测队列 - 机理模型数据
		String QUEUE_SENSOR_MODEL_DATA_ALARM = "queue.sensor.model.data.alarm";
		//机理模型数据报警路由键
		String ROUTING_MODEL_DATA_ALARM = "routing.model.data.alarm";

		//报警等级更新队列
		String QUEUE_ALARM_LEVEL_UPDATE = "queue.alarm.level.update";
		//报警等级更新路由键
		String ROUTING_ALARM_LEVEL_UPDATE = "routing.alarm.level.update";
		//报警等级关闭队列
		String QUEUE_ALARM_LEVEL_CLOSE = "queue.alarm.level.close";
		//报警等级更新路由键
		String ROUTING_ALARM_LEVEL_CLOSE = "routing.alarm.level.close";

		//AI报警交换机
		String EXCHANGE_AI_ALARM = "ai.diagnosis.exchange.value";
		//AI报警消息队列
		String QUEUE_AI_ALARM = "queue.ai.alarm";

		// AI波形交换机
		String EXCHANGE_AI_WAVE = "exchange.ai.wave";

		// Ai波形消息队列
		String QUEUE_AI_WAVE = "queue.ai.wave";

		//机理模型报警交换机
		String EXCHANGE_MECHANISM_MODEL_ALARM = "exchange.mechanism.model.alarm";
		//机理模型报警消息队列
		String QUEUE_MECHANISM_MODEL_ALARM = "queue.mechanism.model.alarm";

		// 消息中心交换机
		String FANOUT_EXCHANGE_MESSAGE = "fanout.exchange.message";
		// 消息中心队列
		String QUEUE_MESSAGE = "queue.message";

		// 润滑策略交换机
		String EXCHANGE_LUBRICATE_STRATEGY = "exchange.lubricate.strategy";
		// 润滑策略路由
		String ROUTING_LUBRICATE_STRATEGY = "routing.lubricate.strategy";
		// 润滑策略消息队列
		String QUEUE_LUBRICATE_STRATEGY = "queue.lubricate.strategy";

		// 钉钉消息交换机
		String EXCHANGE_DINGTALK_MESSAGE = "exchange.dingTalk.message";
		// 钉钉消息路由
		String ROUTING_DINGTALK_MESSAGE = "routing.dingTalk.message";
		// 钉钉消息队列
		String QUEUE_DINGTALK_MESSAGE = "queue.dingTalk.message";
	}

	interface InvalidReason {
		String ABOVE_RANGE_MAX = "原始数据值超过最大量程";

		String INCONSISTENT_SAMPLING_FREQ = "采样频率或采样点数不一致";
		String INCONSISTENT_SAMPLING_POINTS = "波形数据长度和采样点数不一致";

		String HAVE_NO_VALUE_DATA = "原始数据为空";
	}

	interface Cache {

		/**
		 * 采集站&通道、传感器在线状态超时时间（单位：分钟）
		 */
		long COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT = 1440;

		/**
		 * 传感器实例导入成功数
		 */
		String SENSOR_INSTANCE_IMPORT_SUCCESS_NUMBER = "sensor:instance:import:success:number";

		/**
		 * 传感器实例导入第一条失败的序号
		 */
		String SENSOR_INSTANCE_IMPORT_FIRST_FAIL_NUMBER = "sensor:instance:import:first:fail:number";

		/**
		 * 传感器实例导入失败的测点路径
		 */
		String SENSOR_INSTANCE_IMPORT_FAILURE_MONITOR_PATH = "sensor:instance:import:first:fail:monitor:path";

		/**
		 * 传感器实例导入失败信息
		 */
		String SENSOR_INSTANCE_IMPORT_FAILURE_MESSAGE = "sensor:instance:import:failure:message";
	}

	/**
	 * 知识图谱
	 */
	interface KG {
		/**
		 * 知识图谱接口地址base
		 */
		String API_BASE = "http://8.140.207.133:88/api";

		String DEFAULT_TOKEN = "TOKEN";
		String BODY_GRANT = "grant_type";
		String BODY_TENANT = "tenantId";
		String BODY_CLIENT_ID = "clientId";
		String BODY_CLIENT_SECRET = "clientSecret";

		String TOKEN_REDIS_KEY = "szyk:eolm:analyse:kg:token";

		/**
		 * 统一header
		 */
		String HEADER_AUTHORIZATION = "Authorization";

		/**
		 * 统一header
		 */
		String HEADER_SZYK_AUTH = "Szyk-Auth";

		/**
		 * 统一header
		 */
		String HEADER_CONTENT_TYPE ="Content-Type";

		/**
		 * 统一header
		 */
		String HEADER_TENANT_ID = "Tenant-Id";

		/**
		 * 配置
		 */
		String CONTENT_TYPE_URL_ENCODED = "application/x-www-form-urlencoded";

		/**
		 * 配置
		 */
		String CONTENT_TYPE_JSON = "application/json";

		/**
		 * 配置
		 */
		String APP_TENANT_ID = "000000";

		/**
		 * 配置
		 */
		String APP_CLIENT_ID = "saber";

		/**
		 * 配置
		 */
		String APP_CLIENT_SECRET = "saber_secret";

		/**
		 * 配置
		 */
		String APP_GRANT_TYPE = "client";

		/**
		 * 获取token接口
		 */
		String API_AUTH_TOKE = API_BASE + "/szyk-auth/oauth/token";

		/**
		 * 预览：POST {
		 * 				"command":"MATCH (n) WHERE ANY(prop in keys(n) WHERE n[prop] CONTAINS '111') and n.graphId=10086 OPTIONAL MATCH (n)-[r]-(m) RETURN n, r, m",
		 * 			    "graphId":"10086"
		 * 			  }
		 */
		String API_PREVIEW = API_BASE + "/szyk-kg/v1/api/preview";

		/**
		 * 图谱详情：GET id=${id}
		 */
		String API_GRAPH = API_BASE + "/szyk-kg/v1/api/graph";

		/**
		 * 新增or更新实体接口
		 */
		String API_VERTICES_SAVE_OR_UPDATE = API_BASE + "/szyk-kg/v1/api/vertices/saveOrUpdate";

		/**
		 * 新增关系接口
		 */
		String API_RELATION_SAVE = API_BASE + "/szyk-kg/v1/api/relation/save";

		/**
		 * 知识图谱问答机器人：POST {"msg":"xxx"}
		 */
		String API_KBQA = API_BASE + "/szyk-kg/v1/api/kbqa";

		/**
		 * 故障案例知识图谱graphId
		 */
		Long FAULT_CASE_GRAPH_ID = 10086L;

		/**
		 * 故障案例知识图谱label
		 */
		String FAULT_CASE_LABEL = "案例";
	}

	/**
	 * 导入文件格式
	 */
	interface ImportFile {
		/**
		 * 导入文件格式
		 */
		String[] FILE_FORMAT_EXCEL = {".xls", ".xlsx"};
	}

	/**
	 * 文件存储路径
	 */
	interface SaveFileUrl {
		// 普通文件
		String COMMON_FILE = "/file";
		// 3D模型文件
		String THREE_DIMENSION = "/model";
		// 传感器时域波形
		String DIAGNOSIS_WAVE = "/wave";
		// 诊断报告波形
		String DIAGNOSIS_REPORT = "/report";
		// APP安装包
		String APP_WGT = "/wgt";
		// 点检时域波形
		String INSPECT_WAVE = "/inspect";
	}

}
