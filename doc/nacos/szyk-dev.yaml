#spring配置
spring:
  redis:
    ##redis 单机环境配置
    host: r-2zewurygcqi5jmbiv6pd.redis.rds.aliyuncs.com
    port: 6379
    password: RoFJc&sdI2S
    database: 65
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000
  rabbitmq:
    host: *************
    port: 5672
    username: admin
    password: Wmh91@j8d3
    virtual-host: /
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #driver-class-name: org.postgresql.Driver
    #driver-class-name: oracle.jdbc.OracleDriver
    #driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    druid:
      # MySql、PostgreSQL、SqlServer校验
      validation-query: select 1
      # Oracle校验
      #validation-query: select 1 from dual
  kafka:
    custom:
      enable: false
    bootstrap-servers: 127.0.0.1:9092
    producer:
      # 发生错误后，消息重发的次数。
      retries: 0
      #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
      batch-size: 16384
      # 设置生产者内存缓冲区的大小。
      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      # acks: 1
    consumer:
      # 指定默认消费者group id
      group-id: GROUP1
      # 是否自动提交偏移量，默认值是true,为了避免出现重复数据和数据丢失，可以把它设置为false,然后手动提交偏移量
      # 自动提交的时间间隔 在spring boot 2.X 版本中这里采用的是值的类型为Duration 需要符合特定的格式，如1S,1M,2H,5D
      # auto-commit-interval: 1S
      enable-auto-commit: false
      # 该属性指定了消费者在读取一个没有偏移量的分区或者偏移量无效的情况下该作何处理：
      # latest（默认值）在偏移量无效的情况下，消费者将从最新的记录开始读取数据（在消费者启动之后生成的记录）
      # earliest ：在偏移量无效的情况下，消费者将从起始位置读取分区的记录
      auto-offset-reset: latest
      # 键的反序列化方式
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # 值的反序列化方式
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      properties:
        session.timeout.ms: 60000
    listener:
      log-container-config: false
      # 在侦听器容器中运行的线程数。
      concurrency: 2
      #listner负责ack，每调用一次，就立即commit
      ack-mode: manual_immediate
      # missing-topics-fatal: false

#项目模块集中配置
szyk:
  #分布式锁配置
  lock:
    enabled: false
    address: redis://r-2zewurygcqi5jmbiv6pd.redis.rds.aliyuncs.com:6379
  #多团队协作服务配置
  ribbon:
    rule:
      #开启配置
      enabled: true
      #负载均衡优先调用的ip段
      prior-ip-pattern:
        - 192.168.0.*
        - 127.0.0.1
  #通用开发生产环境数据库地址(特殊情况可在对应的子工程里配置覆盖)
  datasource:
    dev:
      # MySql
      url: **********************************************************************************************************************************************************************************************************************************************************************************************
      username: szykdev
      password: 2SSFJc&sGw@
      # PostgreSQL
      #url: *************************************
      #username: postgres
      #password: 123456
      # Oracle
      #url: *************************************
      #username: SZYK
      #password: SZYK
      # SqlServer
      #url: *************************************************
      #username: szyk
      #password: szyk
iot-dc3:
    feign:
        path:
            manager: http://iot-manager-dev.iot-dc3:8400/manager
            auth: http://iot-auth.iot-dc3:8300/auth
