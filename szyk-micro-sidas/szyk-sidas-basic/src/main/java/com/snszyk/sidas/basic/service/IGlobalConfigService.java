/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.basic.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.basic.entity.GlobalConfig;
import com.snszyk.sidas.basic.vo.GlobalConfigVO;

/**
 * 全局配置表 服务类
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
public interface IGlobalConfigService extends BaseService<GlobalConfig> {

	/**
	 * 详情
	 *
	 * @param tenantId
	 * @return
	 */
	GlobalConfigVO detail(String tenantId);

	/**
	 * 提交
	 *
	 * @param globalConfig
	 * @param tenantId
	 * @return
	 */
	boolean submit(GlobalConfigVO globalConfig, String tenantId);

	/**
	 * 获取配置
	 *
	 * @param tenantId 租户id
	 * @param category 类型
	 * @return
	 */
	GlobalConfigVO getSetting(String tenantId, String category);

}
