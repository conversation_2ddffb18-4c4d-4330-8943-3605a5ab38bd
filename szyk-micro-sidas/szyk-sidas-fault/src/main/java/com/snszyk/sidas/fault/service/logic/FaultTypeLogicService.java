/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.fault.service.logic;

import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.fault.service.IFaultTypeService;
import com.snszyk.sidas.fault.vo.FaultTypeVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 设备故障类型表 逻辑服务实现类
 *
 * <AUTHOR>
 * @date 2022/11/30 09:16
 **/
@AllArgsConstructor
@Service
public class FaultTypeLogicService {

    private final IFaultTypeService faultTypeService;

	/**
	 * 搜索设备部位树
	 *
	 * <AUTHOR>
	 * @date 2022/11/30 15:06
	 * @param tenantId
	 * @param parentIds
	 * @return java.util.List<com.snszyk.sidas.basic.vo.EolmTreeVO>
	 */
	public List<FaultTypeVO> searchTypeTree(String tenantId, List<Long> parentIds) {
		List<FaultTypeVO> list;
		List<FaultTypeVO> rootNodes = new ArrayList<>();
		if(Func.isNotEmpty(parentIds)){
			list = faultTypeService.searchTypeTree(tenantId, parentIds);
		} else {
			list = faultTypeService.searchTypeTree(tenantId, null);
		}
		if(Func.isEmpty(list)){
			return rootNodes;
		}
		Map<Long, FaultTypeVO> dataMap = new HashMap<>(16);
		list.forEach(data -> {
			List<String> pathTempList = Arrays.asList(data.getPath().split(","));
			List<String> pathList = pathTempList.subList(pathTempList.size() - 2, pathTempList.size());
			List<String> pathNameTempList = Arrays.asList(data.getPathName().split(","));
			List<String> pathNameList = pathNameTempList.subList(pathNameTempList.size() - 2, pathNameTempList.size());
			for(Integer i = 0; i < pathList.size(); i++){
				FaultTypeVO parent = null;
				if(i > 0) {
					parent = dataMap.get(Func.toLong(pathList.get(i-1)));
					if(Func.isEmpty(parent.getParentId())){
						parent.setParentId(SzykConstant.TOP_PARENT_ID);
						parent.setPath(pathList.get(i-1));
					}
				}
				// 父节点
				FaultTypeVO e = new FaultTypeVO(Func.toLong(pathList.get(i)), pathNameList.get(i), data.getPath());
				e.setParentId(SzykConstant.TOP_PARENT_ID);
				e.setPath(pathList.get(i));
				if(parent != null) {
					e.setParentId(parent.getId());
					e.setPath(data.getPath());
					if(!parent.getChildren().contains(e)){
						parent.getChildren().add(e);
					}
				}
				if(dataMap.get(Func.toLong(pathList.get(i))) == null) {
					dataMap.put(Func.toLong(pathList.get(i)), e);
				}
			}
		});
		for (Long key : dataMap.keySet()) {
			if(Func.equals(dataMap.get(key).getParentId(), SzykConstant.TOP_PARENT_ID)) {
				rootNodes.add(dataMap.get(key));
			}
		}
		return rootNodes;
	}

}
