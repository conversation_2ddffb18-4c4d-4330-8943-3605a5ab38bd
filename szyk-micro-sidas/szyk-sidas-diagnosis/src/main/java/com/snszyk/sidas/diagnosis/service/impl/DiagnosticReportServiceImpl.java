/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.sidas.diagnosis.dto.DiagnosticReportDTO;
import com.snszyk.sidas.diagnosis.entity.DiagnosticReport;
import com.snszyk.sidas.diagnosis.mapper.DiagnosticReportMapper;
import com.snszyk.sidas.diagnosis.service.IDiagnosticReportService;
import com.snszyk.sidas.diagnosis.vo.DiagnosticReportVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 诊断报告表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
@AllArgsConstructor
public class DiagnosticReportServiceImpl extends BaseServiceImpl<DiagnosticReportMapper, DiagnosticReport> implements IDiagnosticReportService {

	@Override
	public IPage<DiagnosticReportDTO> page(IPage<DiagnosticReportDTO> page, DiagnosticReportVO diagnosticReport) {
		return page.setRecords(baseMapper.page(page, diagnosticReport));
	}

}
