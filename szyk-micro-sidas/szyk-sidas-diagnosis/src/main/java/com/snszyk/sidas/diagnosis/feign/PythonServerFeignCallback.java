package com.snszyk.sidas.diagnosis.feign;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Feign失败配置
 *
 * <AUTHOR>
 * @date 2024/01/15 11:38
 **/
@Slf4j
@Component
public class PythonServerFeignCallback implements PythonServerFeign {

	@Override
	public JSONObject freqAndEnvelope(String originTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

}
