/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.sidas.diagnosis.entity.AlarmChart;
import com.snszyk.sidas.diagnosis.vo.AlarmChartVO;

import java.util.List;

/**
 * 报警管理图谱表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
public interface AlarmChartMapper extends BaseMapper<AlarmChart> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param alarmChart
	 * @return
	 */
	List<AlarmChartVO> selectAlarmChartPage(IPage page, AlarmChartVO alarmChart);

}
