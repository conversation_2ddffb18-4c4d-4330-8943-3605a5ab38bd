<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.diagnosis.mapper.AbnormalDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="abnormalDetailResultMap" type="com.snszyk.sidas.diagnosis.entity.AbnormalDetail">
        <id column="id" property="id"/>
        <result column="abnormal_id" property="abnormalId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="wave_id" property="waveId"/>
        <result column="wave_name" property="waveName"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="abnormal_time" property="abnormalTime"/>
        <result column="status" property="status"/>
        <result column="close_time" property="closeTime"/>
    </resultMap>


    <select id="page" resultMap="abnormalDetailResultMap">
        select * from eolm_abnormal_detail
    </select>

</mapper>
