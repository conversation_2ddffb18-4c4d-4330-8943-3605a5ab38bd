<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.diagnosis.mapper.DiagnosisRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="diagnosisRecordResultMap" type="com.snszyk.sidas.diagnosis.entity.DiagnosisRecord">
        <result column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="diagnosis_type" property="diagnosisType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="diagnose_user" property="diagnoseUser"/>
        <result column="diagnose_time" property="diagnoseTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="recordVOResultMap" type="com.snszyk.sidas.diagnosis.vo.DiagnosisRecordVO">
        <result column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="equipment_name" property="equipmentName"/>
        <result column="position_id" property="positionId"/>
        <result column="position_name" property="positionName"/>
        <result column="diagnosis_type" property="diagnosisType"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="diagnose_user" property="diagnoseUser"/>
        <result column="diagnose_time" property="diagnoseTime"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="getList" resultMap="diagnosisRecordResultMap">
        select * from eolm_diagnosis_record where equipment_id = #{record.equipmentId}
        <if test="record.startDate!=null">
            and diagnose_time <![CDATA[ >= ]]> #{record.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="record.endDate!=null">
            and diagnose_time <![CDATA[ <= ]]> #{record.endDate, jdbcType=TIMESTAMP}
        </if>
        order by diagnose_time desc
    </select>


</mapper>
