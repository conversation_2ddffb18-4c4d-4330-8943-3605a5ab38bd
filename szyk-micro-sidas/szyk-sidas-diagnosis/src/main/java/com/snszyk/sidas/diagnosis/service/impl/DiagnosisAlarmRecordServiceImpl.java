/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.sidas.diagnosis.entity.DiagnosisAlarmRecord;
import com.snszyk.sidas.diagnosis.mapper.DiagnosisAlarmRecordMapper;
import com.snszyk.sidas.diagnosis.service.IDiagnosisAlarmRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 诊断报警记录关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-01
 */
@Service
@AllArgsConstructor
public class DiagnosisAlarmRecordServiceImpl extends ServiceImpl<DiagnosisAlarmRecordMapper, DiagnosisAlarmRecord> implements IDiagnosisAlarmRecordService {

}
