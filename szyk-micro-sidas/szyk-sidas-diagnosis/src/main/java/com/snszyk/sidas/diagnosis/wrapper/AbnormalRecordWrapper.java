/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.diagnosis.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.sidas.diagnosis.entity.AbnormalRecord;
import com.snszyk.sidas.diagnosis.vo.AbnormalRecordVO;

import java.util.Objects;

/**
 * 设备异常明细表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
public class AbnormalRecordWrapper extends BaseEntityWrapper<AbnormalRecord, AbnormalRecordVO> {

	public static AbnormalRecordWrapper build() {
		return new AbnormalRecordWrapper();
 	}

	@Override
	public AbnormalRecordVO entityVO(AbnormalRecord abnormalRecord) {
		AbnormalRecordVO abnormalRecordVO = Objects.requireNonNull(BeanUtil.copy(abnormalRecord, AbnormalRecordVO.class));

		//User createUser = UserCache.getUser(abnormalRecord.getCreateUser());
		//User updateUser = UserCache.getUser(abnormalRecord.getUpdateUser());
		//abnormalRecordVO.setCreateUserName(createUser.getName());
		//abnormalRecordVO.setUpdateUserName(updateUser.getName());

		return abnormalRecordVO;
	}

}
