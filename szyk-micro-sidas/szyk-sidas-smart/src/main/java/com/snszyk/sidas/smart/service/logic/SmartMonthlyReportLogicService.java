package com.snszyk.sidas.smart.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.tool.api.R;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO;
import com.snszyk.sidas.smart.service.ISmartMonthlyReportService;
import com.snszyk.sidas.smart.vo.SmartMonthlyReportVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
@Service
@Slf4j
public class SmartMonthlyReportLogicService {

	private final ISmartMonthlyReportService smartMonthlyReportService;
	private final IAttachClient attachClient;

	public IPage<SmartMonthlyReportDTO> selectSmartMonthlyReportPage(IPage<SmartMonthlyReportDTO> page, SmartMonthlyReportVO smartMonthlyReportVO) {
		List<SmartMonthlyReportDTO> list = smartMonthlyReportService.selectSmartMonthlyReportPage(page, smartMonthlyReportVO);
		if (list != null) {
			list.forEach(dto -> {
				if (dto.getAttachId() != null) {
					R<Attach> attachR = attachClient.attachInfoById(dto.getAttachId());
					if (attachR.isSuccess() && attachR.getData() != null) {
						dto.setFile(attachR.getData());
					}
				}
			});
		}
		page.setRecords(list);
		return page;
	}
}
