/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.smart.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.feign.IAttachClient;
import com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO;
import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import com.snszyk.sidas.smart.mapper.SmartMonthlyReportMapper;
import com.snszyk.sidas.smart.service.ISmartMonthlyReportService;
import com.snszyk.sidas.smart.vo.SmartMonthlyReportVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 月度诊断报告 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@AllArgsConstructor
public class SmartMonthlyReportServiceImpl extends BaseServiceImpl<SmartMonthlyReportMapper, SmartMonthlyReport> implements ISmartMonthlyReportService {

	private IAttachClient attachClient;

	@Override
	public IPage<SmartMonthlyReportDTO> selectSmartMonthlyReportPage(IPage<SmartMonthlyReportDTO> page, SmartMonthlyReportVO SmartMonthlyReport) {
		if (SmartMonthlyReport != null) {
			if (StringUtil.isNotBlank(SmartMonthlyReport.getStartTime())) {
				SmartMonthlyReport.setStartTime(SmartMonthlyReport.getStartTime() + " 00:00:00");
			}
			if (StringUtil.isNotBlank(SmartMonthlyReport.getEndTime())) {
				SmartMonthlyReport.setEndTime(SmartMonthlyReport.getEndTime() + " 23:59:59");
			}
		}
		List<SmartMonthlyReportDTO> list = baseMapper.selectSmartMonthlyReportPage(page, SmartMonthlyReport);
		if (list != null) {
			list.forEach(dto -> {
				if (dto.getAttachId() != null) {
					R<Attach> attachR = attachClient.attachInfoById(dto.getAttachId());
					if (attachR.isSuccess() && attachR.getData() != null) {
						dto.setFile(attachR.getData());
					}
				}
			});
		}

		return page.setRecords(list);
	}

}
