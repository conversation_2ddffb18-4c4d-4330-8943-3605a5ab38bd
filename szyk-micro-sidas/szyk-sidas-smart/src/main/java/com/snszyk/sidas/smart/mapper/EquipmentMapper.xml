<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.smart.mapper.EquipmentMapper">
    <select id="getWaveList" resultType="com.snszyk.sidas.smart.dto.WaveDto">
        SELECT w.*
        FROM sidas_wave w
                 LEFT JOIN basic_sensor_instance_param s
                           ON w.sensor_instance_param_id = s.id
        WHERE w.monitor_id = #{monitorId}
          AND s.vibration_type = 1
          AND w.unbind = 0
    </select>

    <select id="waveList" resultType="com.snszyk.sidas.smart.dto.WaveDto">
        SELECT w.*, #{monitorId} as parent_id, s.sampling_freq*1000 as sampling_freq, s.sampling_points,
               t.initial_freq, s.sampling_freq*1000/2.56 as cutoff_freq, s.vibration_type
        FROM sidas_wave w
                 LEFT JOIN basic_sensor_instance_param s ON w.sensor_instance_param_id = s.id
                 LEFT JOIN basic_sensor_instance i ON s.instance_id = i.id
                 LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        WHERE w.monitor_id = #{monitorId} AND w.unbind = 0 and s.vibration_type = 1
    </select>

    <select id="getEquipmentInfo" resultType="com.snszyk.sidas.smart.dto.EquipmentDto">
        select * from eolm_equipment where id = #{equipmentId} and is_deleted = 0
    </select>

    <select id="getEquipmentByMonitorId" resultType="com.snszyk.sidas.smart.dto.EquipmentDto">
        select e.* from eolm_equipment e
                            left join eolm_monitor m
                                      on e.id = m.equipment_id
        where m.id = #{monitorId} and e.is_deleted = 0
    </select>

    <select id="getMonitorList" resultType="com.snszyk.sidas.smart.dto.MonitorDto">
        select * from eolm_monitor where equipment_id = #{equipmentId} and is_deleted = 0
    </select>

    <select id="equipmentAlarm" resultType="com.snszyk.sidas.smart.dto.EquipmentAlarmDto">
        select * from eolm_diagnosis_record where equipment_id = #{equipmentId} order by diagnose_time desc limit 1
    </select>


</mapper>
