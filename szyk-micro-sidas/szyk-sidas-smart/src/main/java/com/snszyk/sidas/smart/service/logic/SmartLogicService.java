package com.snszyk.sidas.smart.service.logic;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.sidas.smart.config.DifyProperties;
import com.snszyk.sidas.smart.constent.DifyConstent;
import com.snszyk.sidas.smart.dto.ConversationHistoryDto;
import com.snszyk.sidas.smart.enums.DifyMessageType;
import com.snszyk.sidas.smart.factory.RequestFactory;
import com.snszyk.sidas.smart.model.BaseRequest;
import com.snszyk.sidas.smart.model.ChatAssistantRequest;
import com.snszyk.sidas.smart.model.ChatflowRequest;
import com.snszyk.sidas.smart.client.SseEmitterClient;
import com.snszyk.sidas.smart.vo.ConversationHistoryVo;
import com.snszyk.sidas.smart.vo.ConversationMessageVo;
import com.snszyk.sidas.smart.vo.MessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmartLogicService {
    private final RequestFactory requestFactory;
    private final DifyProperties difyProperties;

	public SseEmitter processRequest(MessageVo v) {
		log.info("dify请求接口：{}", JSON.toJSONString(difyProperties));
		DifyMessageType type = DifyMessageType.getByCode(v.getType());
		BaseRequest request = requestFactory.createRequest(type);

		// 设置通用属性
		request.setUser(v.getUser())
			.setBaseUrl(difyProperties.getBaseUrl())
			.setInputs(v.getInputs())
			.setAuthorization(difyProperties.getApiKey().get(v.getType()));

		// 根据类型设置特定属性
		switch (type) {
			case CHAT_ASSISTANT:
				request.setUri(DifyConstent.CHAT_ASSISTANT_PATH);
				((ChatAssistantRequest) request)
					.setQuery(v.getContent())
					.setConversationId(v.getConversationId());
				break;
			case CHATFLOW:
				request.setUri(DifyConstent.CHATFLOW_PATH);
				((ChatflowRequest) request)
					.setQuery(v.getContent())
					.setConversationId(v.getConversationId());
				break;
			case WORKFLOW:
				request.setUri(DifyConstent.WORKFLOW_PATH);
				if (StringUtils.isNotBlank(v.getContent())) {
					request.getInputs().put("query", v.getContent());
				}
				break;
			default:
				log.warn("Unsupported message type: {}", type);
				break;
		}

		// 发送请求并处理响应
		return SseEmitterClient.createEmitter(request);
	}

	public ConversationHistoryDto chatflowHistory(ConversationHistoryVo vo) {
		log.info("获取dify通用问答流程历史会话记录：{}", JSON.toJSONString(vo));
		try {
			// 创建RestTemplate实例
			RestTemplate restTemplate = new RestTemplate();

			// 设置请求头
			HttpHeaders headers = new HttpHeaders();
			headers.set("Authorization", "Bearer " + difyProperties.getApiKey().get(vo.getType()));

			// 构建URL和参数
			UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_HISTORY)
				.queryParam("user", vo.getUser());

			if (vo.getLimit() != null) {
				builder.queryParam("limit", vo.getLimit());
			}

			// 创建请求实体
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);

			// 发送GET请求
			ResponseEntity<String> response = restTemplate.exchange(
				builder.toUriString(),
				HttpMethod.GET,
				requestEntity,
				String.class
			);

			// 处理响应
			String responseBody = response.getBody();
			log.info("Dify历史会话记录响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);

			// 解析响应JSON
			return JSON.parseObject(responseBody, ConversationHistoryDto.class);

		} catch (Exception e) {
			log.error("获取Dify历史会话记录异常", e);
			throw new ServiceException("获取历史会话记录失败: " + e.getMessage());
		}
	}

	public JSONArray conversationMessage(ConversationMessageVo vo) {
		log.info("获取dify通用问答会话消息：{}", JSON.toJSONString(vo));
		/*
		 * TODO  get请求，url：difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_MESSAGE,
		 *  header: Authorization: Bearer difyProperties.getApiKey().get(vo.getType())
		 * 	参数：vo.getUser(), vo.getLimit()
		 */
		try {
			// 创建RestTemplate实例
			RestTemplate restTemplate = new RestTemplate();

			// 设置请求头
			HttpHeaders headers = new HttpHeaders();
			headers.set("Authorization", "Bearer " + difyProperties.getApiKey().get(vo.getType()));

			// 构建URL和参数
			UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(difyProperties.getBaseUrl() + DifyConstent.CONVERSATION_MESSAGE)
				.queryParam("user", vo.getUser()).queryParam("conversation_id", vo.getConversationId());
			// 创建请求实体
			HttpEntity<?> requestEntity = new HttpEntity<>(headers);

			// 发送GET请求
			ResponseEntity<String> response = restTemplate.exchange(
				builder.toUriString(),
				HttpMethod.GET,
				requestEntity,
				String.class
			);
			// 处理响应
			String responseBody = response.getBody();
			log.info("Dify历史会话记录响应状态码: {}, 响应内容: {}", response.getStatusCodeValue(), responseBody);
			return JSON.parseObject(responseBody).getJSONArray("data");
		} catch (Exception e) {
			log.error("获取Dify会话消息异常", e);
			throw new ServiceException("获取会话消息失败: " + e.getMessage());
		}
	}
}
