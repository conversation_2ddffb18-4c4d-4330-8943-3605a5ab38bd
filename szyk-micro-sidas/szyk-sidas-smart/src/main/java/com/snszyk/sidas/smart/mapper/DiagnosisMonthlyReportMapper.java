/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.smart.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO;
import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import com.snszyk.sidas.smart.vo.SmartMonthlyReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 月度诊断报告 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface SmartMonthlyReportMapper extends BaseMapper<SmartMonthlyReport> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param SmartMonthlyReport
	 * @return
	 */
	List<SmartMonthlyReportDTO> selectSmartMonthlyReportPage(IPage page,@Param("v") SmartMonthlyReportVO SmartMonthlyReport);

}
