package com.snszyk.sidas.smart.factory;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> Baomingming
 * @create 2023/11/14 17:49
 */
public class OkHttpClientFactory {
	// 默认连接超时时间为2000毫秒
    private static final int DEFAULT_CONNECT_TIMEOUT_MILLIS = 200000;
	// 默认读取超时时间为2000毫秒
    private static final int DEFAULT_READ_TIMEOUT_MILLIS = 200000;
	// 默认连接池大小为5
    private static final int DEFAULT_CONNECTION_POOL_SIZE = 5;
	// 默认连接池等待超时时间为1000毫秒
    private static final int DEFAULT_CONNECTION_POOL_TIMEOUT_MILLIS = 100000;

    private OkHttpClientFactory() {
        // 私有化构造方法，防止外部实例化
    }

        /**
     * 创建一个新的OkHttpClient实例。
     *
     * @return 返回创建好的OkHttpClient实例。
     */
    public static OkHttpClient create() {
        return create(DEFAULT_CONNECT_TIMEOUT_MILLIS, DEFAULT_READ_TIMEOUT_MILLIS,
                DEFAULT_CONNECTION_POOL_SIZE, DEFAULT_CONNECTION_POOL_TIMEOUT_MILLIS);
    }


        /**
     * 创建一个OkHttpClient对象
     *
     * @param connectTimeoutMillis 连接超时时间（毫秒）
     * @param readTimeoutMillis 读取超时时间（毫秒）
     * @param connectionPoolSize 连接池大小
     * @param connectionPoolTimeoutMillis 连接池超时时间（毫秒）
     * @return 一个OkHttpClient对象
     */
    public static OkHttpClient create(int connectTimeoutMillis, int readTimeoutMillis,
                                      int connectionPoolSize, int connectionPoolTimeoutMillis) {
        return new OkHttpClient.Builder()
                .connectTimeout(connectTimeoutMillis, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeoutMillis, TimeUnit.MILLISECONDS)
                .connectionPool(new ConnectionPool(connectionPoolSize, connectionPoolTimeoutMillis,
                                TimeUnit.MILLISECONDS))
                .build();
    }
}

