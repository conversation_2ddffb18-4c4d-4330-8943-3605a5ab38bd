package com.snszyk.sidas.smart.util;

import com.snszyk.core.tool.jackson.JsonUtil;
import com.snszyk.sidas.smart.factory.OkHttpClientFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class WaveClient {

	private final static String HTTP_CONTENT_TYPE ="Content-Type";
	private final static String CONTENT_TYPE = "application/json";
	private static final OkHttpClient CLIENT = OkHttpClientFactory.create();

	public String post(String url, String requestBody) throws IOException {
		log.info("在线模拟数据生成请求体，{}", requestBody);
		log.info("请求url，{}", url);
		Request request = new Request.Builder()
			.url(url)
			.headers(getHeaders())
			.post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"), requestBody))
			.build();

		try (Response response = CLIENT.newCall(request).execute()) {
			if (response.body() == null) {
				return null;
			}
			String result = response.body().string();
			log.info("在线模拟数据生成返回结果，{}", result);
			return result;
		}
	}

	/**
	 * 构建json请求体
	 * @param wavePath
	 * @param samplingFrequency
	 * @return
	 */
	public String buildJsonBody(List<String> wavePath, BigDecimal samplingFrequency) {
		Map<String,Object> map = new HashMap<>();
		map.put("wavePaths",wavePath);
		map.put("samplingFrequency",samplingFrequency);
		return JsonUtil.toJson(map);
	}


	/**
	 * 构建请求头
	 * @return
	 */
	private static Headers getHeaders() {

		Headers.Builder builder = new Headers.Builder();
		builder.add(HTTP_CONTENT_TYPE, CONTENT_TYPE);
		return builder.build();
	}
}
