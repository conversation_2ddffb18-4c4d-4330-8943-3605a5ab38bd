/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.smart.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO;
import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import com.snszyk.sidas.smart.vo.SmartMonthlyReportVO;

/**
 * 月度诊断报告 服务类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface ISmartMonthlyReportService extends BaseService<SmartMonthlyReport> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param SmartMonthlyReport
	 * @return
	 */
	IPage<SmartMonthlyReportDTO> selectSmartMonthlyReportPage(IPage<SmartMonthlyReportDTO> page, SmartMonthlyReportVO SmartMonthlyReport);

}
