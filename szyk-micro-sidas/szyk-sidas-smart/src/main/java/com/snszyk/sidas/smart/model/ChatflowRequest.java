package com.snszyk.sidas.smart.model;

import cn.hutool.json.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChatflowRequest extends BaseRequest {
    private String query;
    private String conversationId;

    @Override
    public String buildRequestBody() {
        JSONObject body = new JSONObject();
        body.set("query", query);
        body.set("user", user);
        body.set("response_mode", responseMode);
		body.set("inputs", inputs);
        if (conversationId != null) {
            body.set("conversation_id", conversationId);
        }
        return body.toString();
    }
}
