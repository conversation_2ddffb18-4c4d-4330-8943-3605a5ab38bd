package com.snszyk.sidas.smart.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.tool.api.R;
import com.snszyk.sidas.smart.annotation.ApiKeyAuth;
import com.snszyk.sidas.smart.dto.ConversationHistoryDto;
import com.snszyk.sidas.smart.dto.DifyFileUploadResponseDto;
import com.snszyk.sidas.smart.service.logic.DiagnosisLogicService;
import com.snszyk.sidas.smart.service.logic.SmartLogicService;
import com.snszyk.sidas.smart.vo.ConversationHistoryVo;
import com.snszyk.sidas.smart.vo.ConversationMessageVo;
import com.snszyk.sidas.smart.vo.MessageVo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/smart")
@AllArgsConstructor
@Slf4j
public class SmartController {

	private final SmartLogicService smartLogicService;
	private final DiagnosisLogicService diagnosisLogicService;

	@PostMapping(path = "/process/request", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "dify请求接口", notes = "传入MessageVo")
	public SseEmitter processRequest(@RequestBody MessageVo v) {
		if (v.getMonitorId() != null) {
			String equipmentInfo = diagnosisLogicService.getEquipmentByMonitorId(v.getMonitorId());
			Map<String, Object> inputs = v.getInputs();
			if (inputs != null) {
				inputs.put("equipment", equipmentInfo);
			}
		}
		if (v.getEquipmentId() != null) {
			try {
				long start = System.currentTimeMillis();
				diagnosisLogicService.waveInfo(v.getEquipmentId(), v.getDesc(), v.getInputs());
				long end = System.currentTimeMillis();
				log.info("waveInfo耗时：{}s", (end - start) / 1000.0);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}
		return smartLogicService.processRequest(v);
	}

	/**
	 * Dify文件上传接口
	 *
	 * @param file 上传的文件
	 * @param user 用户标识
	 * @return Dify文件上传响应信息
	 */
	@PostMapping("/files/upload")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "Dify文件上传接口", notes = "上传文件到Dify平台，用于多模态理解")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "file", value = "要上传的文件", required = true, dataType = "__file"),
		@ApiImplicitParam(name = "user", value = "用户标识，用于定义终端用户的身份", required = true, dataType = "string"),
		@ApiImplicitParam(name = "type", value = "权限标识", required = true, dataType = "string")
	})
	public R<DifyFileUploadResponseDto> uploadFileToDify(
		@RequestParam("file") MultipartFile file,
		@RequestParam("user") String user,
		@RequestParam("type") String type) {
		return R.data(smartLogicService.uploadFileToDify(file, user, type));
	}

	@ApiKeyAuth
	@GetMapping("/equipmentAlarm")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "设备告警信息", notes = "根据设备id获取设备告警信息")
	public String equipmentAlarm(Long equipmentId) {
		String s = diagnosisLogicService.equipmentAlarm(equipmentId);
		log.info("设备告警信息：{}", s);
		return s;
	}

	/**
	 * 获取dify通用问答流程历史会话记录
	 */
	@ApiKeyAuth
	@GetMapping("/chatflowHistory")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "获取dify通用问答流程历史会话记录", notes = "获取dify通用问答流程历史会话记录（默认20条）")
	public R<ConversationHistoryDto> chatflowHistory(ConversationHistoryVo vo) {
		return R.data(smartLogicService.chatflowHistory(vo));
	}
	/**
	 * 获取dify通用问答会话消息
	 */
	@ApiKeyAuth
	@GetMapping("/conversationMessage")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "获取dify通用问答会话消息", notes = "获取dify通用问答会话消息")
	public R conversationMessage(ConversationMessageVo vo) {
		return R.data(smartLogicService.conversationMessage(vo));
	}

	/**
	 * 获取下一步建议
	 */
	@ApiKeyAuth
	@GetMapping("/next-step-suggestion")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "获取下一步建议", notes = "获取下一步建议")
	public R nextStepSuggestion(Map<String,Object> map) {
		return R.data(smartLogicService.nextStepSuggestion(vo));
	}
}
