<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.sidas.smart.mapper.SmartMonthlyReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="SmartMonthlyReportResultMap" type="com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="data_start" property="dataStart"/>
        <result column="data_end" property="dataEnd"/>
        <result column="attach_id" property="attachId"/>
        <result column="file_name" property="fileName"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="selectSmartMonthlyReportPage" resultMap="SmartMonthlyReportResultMap">
        SELECT
            *
        FROM eolm_diagnosis_monthly_report
        WHERE is_deleted = 0
        <if test="v.startTime != null">
            AND create_time >= #{v.startTime}
        </if>
        <if test="v.dataEnd != null">
            AND create_time &lt;= #{v.endTime}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
