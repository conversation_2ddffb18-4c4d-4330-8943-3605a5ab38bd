/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.sidas.smart.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.sidas.smart.dto.SmartMonthlyReportDTO;
import com.snszyk.sidas.smart.entity.SmartMonthlyReport;
import com.snszyk.sidas.smart.service.ISmartMonthlyReportService;
import com.snszyk.sidas.smart.vo.SmartMonthlyReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 月度诊断报告 控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/diagnosis-monthly-report")
@Api(value = "月度诊断报告", tags = "月度诊断报告接口")
public class SmartMonthlyReportController extends SzykController {

	private final ISmartMonthlyReportService SmartMonthlyReportService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入SmartMonthlyReport")
	public R<SmartMonthlyReport> detail(SmartMonthlyReport SmartMonthlyReport) {
		SmartMonthlyReport detail = SmartMonthlyReportService.getOne(Condition.getQueryWrapper(SmartMonthlyReport));
		return R.data(detail);
	}

	/**
	 * 分页 月度诊断报告
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入SmartMonthlyReport")
	public R<IPage<SmartMonthlyReport>> list(SmartMonthlyReport SmartMonthlyReport, Query query) {
		IPage<SmartMonthlyReport> pages = SmartMonthlyReportService.page(Condition.getPage(query), Condition.getQueryWrapper(SmartMonthlyReport));
		return R.data(pages);
	}

	/**
	 * 自定义分页 月度诊断报告
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入SmartMonthlyReport")
	public R<IPage<SmartMonthlyReportDTO>> page(SmartMonthlyReportVO SmartMonthlyReport, Query query) {
		IPage<SmartMonthlyReportDTO> pages = SmartMonthlyReportService.selectSmartMonthlyReportPage(Condition.getPage(query), SmartMonthlyReport);
		return R.data(pages);
	}

	/**
	 * 新增 月度诊断报告
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入SmartMonthlyReport")
	public R save(@Valid @RequestBody SmartMonthlyReport SmartMonthlyReport) {
		return R.status(SmartMonthlyReportService.save(SmartMonthlyReport));
	}

	/**
	 * 修改 月度诊断报告
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入SmartMonthlyReport")
	public R update(@Valid @RequestBody SmartMonthlyReport SmartMonthlyReport) {
		return R.status(SmartMonthlyReportService.updateById(SmartMonthlyReport));
	}

	/**
	 * 新增或修改 月度诊断报告
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入SmartMonthlyReport")
	public R submit(@Valid @RequestBody SmartMonthlyReport SmartMonthlyReport) {
		return R.status(SmartMonthlyReportService.saveOrUpdate(SmartMonthlyReport));
	}

	/**
	 * 删除 月度诊断报告
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(SmartMonthlyReportService.deleteLogic(Func.toLongList(ids)));
	}

}
