package com.snszyk.sidas.smart.service.logic;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.sidas.smart.config.WaveProperties;
import com.snszyk.sidas.smart.dto.EquipmentAlarmDto;
import com.snszyk.sidas.smart.dto.EquipmentDto;
import com.snszyk.sidas.smart.dto.MonitorDto;
import com.snszyk.sidas.smart.dto.WaveDto;
import com.snszyk.sidas.smart.mapper.EquipmentMapper;
import com.snszyk.sidas.smart.client.WaveClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class DiagnosisLogicService {

	@Resource
    private EquipmentMapper equipmentMapper;
	@Resource
    private InfluxdbTools influxdbTools;
	@Resource
    private WaveProperties waveProperties;
	@Resource
    private WaveClient waveClient;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String START_DATE = "2025-01-01 00:00:00";
    private static final String END_DATE = "2025-04-20 23:59:59";

    public String getEquipmentByMonitorId(Long monitorId) {
        return Optional.ofNullable(equipmentMapper.getEquipmentByMonitorId(monitorId))
                .map(dto -> dto.convertLLmPrompt().toString())
                .orElse("");
    }

    private Long dateConvert(String date) {
        return LocalDateTime.parse(date, DATE_FORMATTER)
                .toInstant(ZoneOffset.UTC)
                .toEpochMilli();
    }
	/**
	 * 获取波形信息
	 *
	 * @param equipmentId 设备ID
	 * @param desc 描述信息
	 * @param inputs 输入参数Map
	 * @throws IOException 当波形处理发生IO异常时
	 */
	public void waveInfo(Long equipmentId, String desc, Map<String, Object> inputs) throws IOException {
		// 获取设备信息
		EquipmentDto equipment = equipmentMapper.getEquipmentInfo(equipmentId);
		StringBuilder equipmentStr = equipment.convertLLmPrompt();
		equipmentStr.append("<br>2.设备部位：<br>&emsp;&emsp;");

		// 获取监测点列表
		List<MonitorDto> monitorList = equipmentMapper.getMonitorList(equipmentId);
		Map<String, MonitorDto> waveformMap = new HashMap<>();
		AtomicReference<BigDecimal> samplingFrequency = new AtomicReference<>(BigDecimal.ZERO);

		// 处理每个监测点的波形数据
		monitorList.forEach(monitorDto -> {
			equipmentStr.append(monitorDto.getName()).append(",");
			processMonitorWaveforms(monitorDto, waveformMap, samplingFrequency);
		});

		// 添加故障描述
		equipmentStr.append("<br>3.故障现象：").append(desc);

		// 构建波形提示
		StringBuilder wavePrompt = new StringBuilder("波形图与部位对应关系：");

		// 处理波形数据
		List<String> waveFiles = processWaveforms(new ArrayList<>(waveformMap.keySet()),
			waveformMap, samplingFrequency.get(), wavePrompt);

		// 更新输入参数
		updateInputs(inputs, equipmentId, equipmentStr.toString(), waveFiles, wavePrompt);
	}

	/**
	 * 处理监测点的波形数据
	 */
	private void processMonitorWaveforms(MonitorDto monitor, Map<String, MonitorDto> waveformMap,
										 AtomicReference<BigDecimal> samplingFrequency) {
		List<WaveDto> waves = equipmentMapper.waveList(monitor.getId());
		if (CollectionUtils.isEmpty(waves)) {
			return;
		}

		WaveDto latestWave = waves.get(0);
		samplingFrequency.set(latestWave.getSamplingFreq());

		JSONObject waveform = mainWaveForm(monitor.getId(), latestWave.getId());
		if (waveform != null && waveform.getString("waveformUrl") != null) {
			String waveformUrl = waveProperties.getFileUrl() + waveform.getString("waveformUrl");
			waveformMap.put(waveformUrl, monitor);
		}
	}

	/**
	 * 处理波形数据并获取波形文件列表
	 */
	private List<String> processWaveforms(List<String> waveUrls, Map<String, MonitorDto> waveformMap,
										  BigDecimal samplingFrequency, StringBuilder wavePrompt) throws IOException {
		long startTime = System.currentTimeMillis();
		String result = waveClient.post(
			waveProperties.getUrl() + waveProperties.getWaveRoute(),
			waveClient.buildJsonBody(waveUrls, samplingFrequency)
		);
		logExecutionTime(startTime);

		List<String> waveFiles = new ArrayList<>();
		if (result != null) {
			List<String> resultList = JSON.parseArray(result, String.class);
			int existPic = 0;
			for (int i = 0; i < resultList.size(); i++) {
				if (StringUtils.isNotBlank(resultList.get(i))) {
					existPic++;
					MonitorDto dto = waveformMap.get(waveUrls.get(i));
					joinStr(wavePrompt, dto, existPic);
					waveFiles.add(resultList.get(i));
				}
			}
		}
		return waveFiles;
	}

	/**
	 * 拼接监测点信息字符串
	 *
	 * @param sb 字符串构建器
	 * @param dto 监测点DTO
	 * @param index 图片序号
	 * @return 拼接后的字符串构建器
	 */
	private StringBuilder joinStr(StringBuilder sb, MonitorDto dto, int index) {
		return sb.append("<br>图")
			.append(index)
			.append("是")
			.append(dto.getName())           // 部位名称
			.append("部位的波形图");
	}

	/**
	 * 更新输入参数Map
	 */
	private void updateInputs(Map<String, Object> inputs, Long equipmentId,
							  String equipmentInfo, List<String> waveFiles, StringBuilder wavePrompt) {
		inputs.put("equipment", equipmentInfo);
		inputs.put("equipmentId", String.valueOf(equipmentId));
		inputs.put("waveFiles", generateDifyFiles(waveFiles));
		inputs.put("wavePrompt", wavePrompt.toString());
	}

	/**
	 * 生成波形文件的JSON数组
	 */
	private JSONArray generateDifyFiles(List<String> waveFileIds) {
		JSONArray array = new JSONArray();
		waveFileIds.forEach(id -> {
			JSONObject obj = new JSONObject();
			obj.put("type", "image");
			obj.put("transfer_method", "local_file");
			obj.put("upload_file_id", id);
			array.add(obj);
		});
		return array;
	}

	/**
	 * 获取设备报警信息
	 *
	 * @param equipmentId 设备ID
	 * @return 报警信息的LLM提示字符串
	 */
	public String equipmentAlarm(Long equipmentId) {
		EquipmentAlarmDto alarmDto = equipmentMapper.equipmentAlarm(equipmentId);
		return alarmDto != null ? alarmDto.convertLLmPrompt() : "";
	}

	/**
	 * 获取主波形数据
	 *
	 * @param monitorId 监测点ID
	 * @param waveId 波形ID
	 * @return 波形数据JSON对象
	 */
	public JSONObject mainWaveForm(Long monitorId, Long waveId) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("monitorId", monitorId);
		jsonObject.put("waveId", waveId);
		jsonObject.put("invalid", "0");

		List<JSONObject> list = influxdbTools.queryDataTable(
			dateConvert(START_DATE),
			dateConvert(END_DATE),
			jsonObject,
			null,
			(sql) -> {
				sql.getQuerySQL().append(" |>sort(columns: [\"_time\"], desc: true) ")
					.append(" |>limit(n: 1)");
				sql.addSort("_time", false);
			},
			(item) -> {
				JSONObject object = new JSONObject();
				object.put("waveId", waveId);
				object.put("sampleDataType", item.getValueByKey("sampleDataType"));
				object.put("monitorId", item.getValueByKey("monitorId"));
				object.put("originTime", item.getTime().toEpochMilli());
				object.put("value", item.getValueByKey("value"));
				object.put("waveformUrl", item.getValueByKey("waveformUrl"));
				return object;
			});
		return list.isEmpty() ? null : list.get(0);
	}

	/**
	 * 记录执行时间日志
	 */
	private void logExecutionTime(long startTime) {
		double executionTimeSeconds = (System.currentTimeMillis() - startTime) / 1000.0;
		log.info("执行耗时：{}s", executionTimeSeconds);
	}
}
