package com.snszyk.sidas.smart.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DifyMessageType {
    CHAT_ASSISTANT("chat-assistant", "聊天助手"),
    CHATFLOW("chatflow", "Chatflow对话"),
    WORK<PERSON>OW("workflow", "工作流");

    private final String code;
    private final String desc;

    public static DifyMessageType getByCode(String code) {
        for (DifyMessageType type : values()) {
            if (code.contains(type.getCode())) {
                return type;
            }
        }
        return CHATFLOW;
    }
}
