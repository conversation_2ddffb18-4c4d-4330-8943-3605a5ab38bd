package com.snszyk.sidas.smart.client;

import com.alibaba.fastjson.JSON;
import com.snszyk.sidas.smart.model.BaseRequest;
import io.netty.channel.ChannelOption;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.util.retry.Retry;

import java.io.IOException;
import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE发射器工具类
 * <p>
 * 该工具类用于创建和管理SSE连接，处理服务器发送事件(Server-Sent Events)。
 * 提供了创建SSE连接、订阅事件流、处理消息和错误的功能。
 * 使用响应式编程模型与后端服务通信，支持重试机制和资源管理。
 */
@Slf4j
public class SseEmitterClient {
    /**
     * SSE发射器超时时间，默认5分钟
     */
    private static final long EMITTER_TIMEOUT = 300_000L;

    /**
     * HTTP连接超时时间，默认10秒
     */
    private static final int CONNECT_TIMEOUT = 100000;

    /**
     * HTTP响应超时时间，默认30秒
     */
    private static final long RESPONSE_TIMEOUT = 300_000L;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRIES = 3;

    /**
     * 重试间隔时间（5s）
     */
    private static final Duration RETRY_DELAY = Duration.ofSeconds(5);

    /**
     * 存储所有活动的SSE发射器实例
     * 使用ConcurrentHashMap确保线程安全
     * key: 发射器ID，由用户ID和会话ID组成
     * value: SseEmitter实例
     */
    private static final ConcurrentHashMap<String, SseEmitter> activeEmitters = new ConcurrentHashMap<>();

    /**
     * 创建SSE发射器
     * <p>
     * 该方法是工具类的主要入口点，用于创建新的SSE连接并订阅事件流。
     * 它完成以下操作：
     * 1. 生成唯一的发射器ID
     * 2. 创建SSE发射器并设置超时时间
     * 3. 将发射器存储在活动发射器集合中
     * 4. 创建WebClient并订阅事件流
     * 5. 配置发射器的回调函数
     *
     * @param request 聊天请求参数，包含创建SSE连接所需的所有信息
     * @return 新创建的SseEmitter实例
     */
    public static SseEmitter createEmitter(BaseRequest request) {
		log.info("createEmitter - request = " + JSON.toJSONString(request));
        // 生成唯一的发射器ID
        String emitterId = generateEmitterId(request.getUser());
		log.info("createEmitter - emitterId = " + emitterId);

        // 创建SSE发射器并设置超时时间
        SseEmitter emitter = new SseEmitter(EMITTER_TIMEOUT);
        // 将发射器存储在活动发射器集合中
        activeEmitters.put(emitterId, emitter);

        try {
            // 创建WebClient
            WebClient webClient = createWebClient(request.getBaseUrl());
            // 订阅事件流
            subscribeToEventStream(webClient, request, emitter);
            // 配置发射器的回调函数
            configureEmitterCallbacks(emitter, emitterId);
        } catch (Exception e) {
            // 处理创建过程中的错误
            handleEmitterError(emitter, emitterId, e);
        }
		return emitter;
    }

	/**
	 * 生成唯一的发射器ID
	 * @param user
	 * @return
	 */
	private static String generateEmitterId(String user) {
		return user + ":" + UUID.randomUUID().toString();
	}

    /**
     * 创建WebClient实例
     * <p>
     * 配置并创建一个带有自定义设置的WebClient实例，用于与服务器进行响应式通信。
     * 设置包括：
     * 1. 连接超时时间
     * 2. 响应超时时间
     * 3. 基础URL
     *
     * @param baseUrl 服务器的基础URL
     * @return 配置好的WebClient实例
     */
    private static WebClient createWebClient(String baseUrl) {
		log.info("createWebClient - baseUrl = " + baseUrl);
        // 创建HTTP客户端并配置超时设置
        HttpClient httpClient = HttpClient.create()
            // 设置连接超时
            .tcpConfiguration(tcpClient -> tcpClient
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, CONNECT_TIMEOUT))
            // 设置响应超时
            .responseTimeout(Duration.ofMillis(RESPONSE_TIMEOUT));

        // 构建WebClient
        return WebClient.builder()
            // 使用自定义的HTTP客户端
            .clientConnector(new ReactorClientHttpConnector(httpClient))
            // 设置基础URL
            .baseUrl(baseUrl)
            .build();
    }

    /**
     * 订阅事件流
     * <p>
     * 使用WebClient发送请求并订阅服务器发送的事件流。
     * 该方法完成以下操作：
     * 1. 创建SSE事件类型引用
     * 2. 配置请求头和请求体
     * 3. 设置错误处理和重试策略
     * 4. 订阅事件流并处理事件
     *
     * @param client WebClient实例，用于发送HTTP请求
     * @param request 聊天请求参数
     * @param emitter SSE发射器实例，用于向客户端发送事件
     */
    private static void subscribeToEventStream(WebClient client, BaseRequest request, SseEmitter emitter) {
		log.info("request header：{}", request.getAuthorization());
		log.info("request body：{}", request.buildRequestBody());
        // 创建SSE事件类型引用
        ParameterizedTypeReference<ServerSentEvent<String>> type =
            new ParameterizedTypeReference<ServerSentEvent<String>>() {};
        // 配置请求并创建事件流
        Flux<ServerSentEvent<String>> eventStream = client.post()
            // 设置请求URI
            .uri(request.getUri())
            // 添加授权头
            .header("Authorization", "Bearer " + request.getAuthorization())
            // 设置内容类型
            .header("Content-Type", "application/json")
            // 设置请求体
            .bodyValue(request.buildRequestBody())
            .retrieve()
            // 处理HTTP错误状态
            .onStatus(HttpStatus::isError, SseEmitterClient::handleErrorResponse)
            // 将响应转换为事件流
            .bodyToFlux(type)
            // 配置重试策略
            .retryWhen(Retry.backoff(MAX_RETRIES, RETRY_DELAY)
                .doBeforeRetry(signal -> log.warn("Retrying after error: {}", signal.failure().getMessage())));

        // 订阅事件流
        eventStream.subscribe(
            // 处理收到的内容
            content -> handleContent(content, emitter),
            // 处理错误
            error -> handleError(error, emitter),
            // 处理完成
            () -> handleCompletion(emitter, request.getUser())
        );
    }

    /**
     * 处理接收到的事件内容
     * <p>
     * 将从服务器接收到的事件内容发送给客户端。
     * 如果发送过程中发生错误，则完成发射器并返回错误。
     *
     * @param content 从服务器接收到的事件内容
     * @param emitter SSE发射器实例
     */
    private static void handleContent(ServerSentEvent<String> content, SseEmitter emitter) {
        // 检查内容是否有效
        if (content != null && content.data() != null) {
            try {
                // 将内容发送给客户端
                emitter.send(SseEmitter.event().data(content.data()));
            } catch (IOException e) {
                // 处理发送错误
                log.error("Failed to send data to emitter", e);
                // 完成发射器并返回错误
                emitter.completeWithError(e);
            }
        }
    }

    /**
     * 处理HTTP错误响应
     * <p>
     * 将HTTP错误响应转换为异常对象，以便进一步处理。
     * 该方法会提取错误响应体并记录错误日志。
     *
     * @param response HTTP错误响应
     * @return 包含异常对象的Mono
     */
    private static Mono<? extends Throwable> handleErrorResponse(ClientResponse response) {
        // 将响应体转换为字符串
        return response.bodyToMono(String.class)
            .map(errorBody -> {
                // 记录错误日志
                log.error("Server error: {} with status: {}", errorBody, response.statusCode());
                // 创建并返回异常对象
                return new RuntimeException("Server error: " + errorBody);
            });
    }

    /**
     * 处理流式事件错误
     * <p>
     * 处理事件流中发生的错误，记录错误日志并完成发射器。
     *
     * @param error 发生的错误
     * @param emitter SSE发射器实例
     */
    private static void handleError(Throwable error, SseEmitter emitter) {
        // 记录错误日志
        log.error("Stream error", error);
        // 完成发射器并返回错误
        emitter.completeWithError(error);
    }

    /**
     * 处理流式事件完成
     * <p>
     * 当事件流正常完成时调用此方法。
     * 它会从活动发射器集合中移除发射器并正常完成发射器。
     *
     * @param emitter SSE发射器实例
     * @param emitterId 发射器ID
     */
    private static void handleCompletion(SseEmitter emitter, String emitterId) {
        // 记录完成日志
        log.info("Stream completed for emitter: {}", emitterId);
        // 从活动发射器集合中移除
        activeEmitters.remove(emitterId);
        // 正常完成发射器
        emitter.complete();
    }

    /**
     * 处理发射器创建错误
     * <p>
     * 当创建SSE发射器过程中发生错误时调用此方法。
     * 它会记录错误日志，从活动发射器集合中移除发射器，并完成发射器。
     *
     * @param emitter SSE发射器实例
     * @param emitterId 发射器ID
     * @param e 发生的异常
     */
    private static void handleEmitterError(SseEmitter emitter, String emitterId, Exception e) {
        // 记录错误日志
        log.error("Error creating emitter: {}", emitterId, e);
        // 从活动发射器集合中移除
        activeEmitters.remove(emitterId);
        // 完成发射器并返回错误
        emitter.completeWithError(e);
    }

    /**
     * 配置发射器回调函数
     * <p>
     * 为SSE发射器设置完成和超时回调函数。
     * 这些回调函数用于在发射器完成或超时时清理资源。
     *
     * @param emitter SSE发射器实例
     * @param emitterId 发射器ID
     */
    private static void configureEmitterCallbacks(SseEmitter emitter, String emitterId) {
        // 设置完成回调
        emitter.onCompletion(() -> {
            // 记录完成日志
            log.info("Emitter completed: {}", emitterId);
            // 从活动发射器集合中移除
            activeEmitters.remove(emitterId);
        });

        // 设置超时回调
        emitter.onTimeout(() -> {
            // 记录超时日志
            log.warn("Emitter timed out: {}", emitterId);
            // 从活动发射器集合中移除
            activeEmitters.remove(emitterId);
        });
    }

    /**
     * 清理所有活动的发射器
     * <p>
     * 关闭并清理所有活动的SSE发射器。
     * 该方法通常在应用关闭或需要重置所有连接时调用。
     * 它会尝试正常完成每个发射器，并处理可能发生的异常。
     */
    public static void cleanup() {
        // 遍历所有活动的发射器
        activeEmitters.forEach((id, emitter) -> {
            try {
                // 尝试正常完成发射器
                emitter.complete();
            } catch (Exception e) {
                // 记录清理过程中的错误
                log.error("Error during cleanup for emitter: {}", id, e);
            }
        });
        // 清空发射器集合
        activeEmitters.clear();
    }
}
