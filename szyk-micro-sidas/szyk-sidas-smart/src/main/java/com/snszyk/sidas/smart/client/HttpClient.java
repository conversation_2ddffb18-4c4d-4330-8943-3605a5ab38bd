package com.snszyk.sidas.smart.client;

import com.snszyk.sidas.smart.factory.OkHttpClientFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 通用HTTP客户端
 * 提供简洁的GET和POST请求方法
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class HttpClient {

    private static final String CONTENT_TYPE_JSON = "application/json; charset=utf-8";
    private static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded; charset=utf-8";
    private static final OkHttpClient CLIENT = OkHttpClientFactory.create();

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String get(String url) throws IOException {
        return get(url, null);
    }

    /**
     * 发送GET请求（带请求头）
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String get(String url, Map<String, String> headers) throws IOException {
        log.info("发送GET请求，URL: {}", url);

        Request.Builder requestBuilder = new Request.Builder().url(url);

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("GET请求失败，状态码: {}, URL: {}", response.code(), url);
                throw new IOException("请求失败: " + response.code());
            }

            if (response.body() == null) {
                log.warn("GET请求响应体为空，URL: {}", url);
                return null;
            }

            String result = response.body().string();
            log.info("GET请求成功，URL: {}, 响应长度: {}", url, result.length());
            return result;
        }
    }

    /**
     * 发送POST请求（JSON格式）
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String post(String url, String jsonBody) throws IOException {
        return post(url, jsonBody, null);
    }

    /**
     * 发送POST请求（JSON格式，带请求头）
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String post(String url, String jsonBody, Map<String, String> headers) throws IOException {
        log.info("发送POST请求，URL: {}, 请求体长度: {}", url, jsonBody != null ? jsonBody.length() : 0);

        RequestBody requestBody = RequestBody.create(
            MediaType.parse(CONTENT_TYPE_JSON),
            jsonBody != null ? jsonBody : ""
        );

        return executePost(url, requestBody, headers);
    }

    /**
     * 发送POST请求（表单格式）
     *
     * @param url 请求URL
     * @param formData 表单数据
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String postForm(String url, Map<String, String> formData) throws IOException {
        return postForm(url, formData, null);
    }

    /**
     * 发送POST请求（表单格式，带请求头）
     *
     * @param url 请求URL
     * @param formData 表单数据
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String postForm(String url, Map<String, String> formData, Map<String, String> headers) throws IOException {
        log.info("发送POST表单请求，URL: {}, 表单字段数: {}", url, formData != null ? formData.size() : 0);

        FormBody.Builder formBuilder = new FormBody.Builder();
        if (formData != null) {
            formData.forEach(formBuilder::add);
        }

        return executePost(url, formBuilder.build(), headers);
    }

    /**
     * 执行POST请求的通用方法
     *
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @return 响应内容
     * @throws IOException 请求异常
     */
    private String executePost(String url, RequestBody requestBody, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
            .url(url)
            .post(requestBody);

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("POST请求失败，状态码: {}, URL: {}", response.code(), url);
                throw new IOException("请求失败: " + response.code());
            }

            if (response.body() == null) {
                log.warn("POST请求响应体为空，URL: {}", url);
                return null;
            }

            String result = response.body().string();
            log.info("POST请求成功，URL: {}, 响应长度: {}", url, result.length());
            return result;
        }
    }

    /**
     * 发送GET请求（带超时设置）
     *
     * @param url 请求URL
     * @param headers 请求头
     * @param timeoutSeconds 超时时间（秒）
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String get(String url, Map<String, String> headers, int timeoutSeconds) throws IOException {
        log.info("发送GET请求（自定义超时），URL: {}, 超时: {}秒", url, timeoutSeconds);

        OkHttpClient customClient = CLIENT.newBuilder()
            .connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .build();

        Request.Builder requestBuilder = new Request.Builder().url(url);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = customClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("GET请求失败，状态码: {}, URL: {}", response.code(), url);
                throw new IOException("请求失败: " + response.code());
            }

            if (response.body() == null) {
                log.warn("GET请求响应体为空，URL: {}", url);
                return null;
            }

            String result = response.body().string();
            log.info("GET请求成功，URL: {}, 响应长度: {}", url, result.length());
            return result;
        }
    }

    /**
     * 发送POST请求（带超时设置）
     *
     * @param url 请求URL
     * @param jsonBody JSON请求体
     * @param headers 请求头
     * @param timeoutSeconds 超时时间（秒）
     * @return 响应内容
     * @throws IOException 请求异常
     */
    public String post(String url, String jsonBody, Map<String, String> headers, int timeoutSeconds) throws IOException {
        log.info("发送POST请求（自定义超时），URL: {}, 请求体长度: {}, 超时: {}秒",
                url, jsonBody != null ? jsonBody.length() : 0, timeoutSeconds);

        OkHttpClient customClient = CLIENT.newBuilder()
            .connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
            .build();

        RequestBody requestBody = RequestBody.create(
            MediaType.parse(CONTENT_TYPE_JSON),
            jsonBody != null ? jsonBody : ""
        );

        Request.Builder requestBuilder = new Request.Builder()
            .url(url)
            .post(requestBody);

        if (headers != null && !headers.isEmpty()) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = customClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("POST请求失败，状态码: {}, URL: {}", response.code(), url);
                throw new IOException("请求失败: " + response.code());
            }

            if (response.body() == null) {
                log.warn("POST请求响应体为空，URL: {}", url);
                return null;
            }

            String result = response.body().string();
            log.info("POST请求成功，URL: {}, 响应长度: {}", url, result.length());
            return result;
        }
    }
}
