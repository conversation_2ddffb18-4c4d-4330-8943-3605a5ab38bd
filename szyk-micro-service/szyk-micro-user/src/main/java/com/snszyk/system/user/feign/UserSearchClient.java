/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.system.user.feign;

import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.user.entity.User;
import com.snszyk.system.user.service.IUserSearchService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户查询服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
public class UserSearchClient implements IUserSearchClient {

	private final IUserSearchService service;

	@Override
	@GetMapping(LIST_ALL_USER)
	public R<List<User>> listAllUser(String tenantId) {
		return R.data(service.listAllUser(tenantId));
	}

	@Override
	@GetMapping(LIST_BY_USER)
	public R<List<User>> listByUser(String userId) {
		return R.data(service.listByUser(Func.toLongList(userId)));
	}

	@Override
	@GetMapping(LIST_BY_DEPT)
	public R<List<User>> listByDept(String deptId) {
		return R.data(service.listByDept(Func.toLongList(deptId)));
	}

	@Override
	@GetMapping(LIST_BY_POST)
	public R<List<User>> listByPost(String postId) {
		return R.data(service.listByPost(Func.toLongList(postId)));
	}

	@Override
	@GetMapping(LIST_BY_ROLE)
	public R<List<User>> listByRole(String roleId) {
		return R.data(service.listByRole(Func.toLongList(roleId)));
	}

	@Override
	@GetMapping(LIST_BY_DEPT_ROLE)
	public R<List<User>> listByDeptRole(String deptId, String roleId) {
		return R.data(service.listByDeptRole(Func.toLongList(deptId), Func.toLongList(roleId)));
	}

}
