package com.snszyk.message.feign;

import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessagePushVo;
import com.snszyk.message.vo.MessageVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息feign客户端
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
public class MessageClient implements IMessageClient{

	private MessageLogicService messageLogicService;

	@Override
	@PostMapping(PUSH_MESSAGE)
	public R pushMessage(MessageVo v) {
		return messageLogicService.commitMessage(v);
	}

	@Override
	@PostMapping(READ_MESSAGE)
	public R readMessage(MessagePushVo v) {
		return R.status(messageLogicService.readMessage(v));
	}

}
