/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.message.dto.MessagePushDto;
import com.snszyk.message.entity.MessagePush;
import com.snszyk.message.mapper.MessagePushMapper;
import com.snszyk.message.service.IMessagePushService;
import com.snszyk.message.vo.MessagePushVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 消息推送service
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class MessagePushServiceImpl extends BaseServiceImpl<MessagePushMapper, MessagePush> implements IMessagePushService {

	/**
	 * 消息接收分页列表
	 * @param vo vo
	 * @return
	 */
	@Override
	public IPage<MessagePushDto> pageReceive(IPage<MessagePushDto> page, MessagePushVo vo) {
		return page.setRecords(baseMapper.pageReceive(page, vo));
	}

	@Override
	public boolean updateAllReadMessagePushVo(MessagePushVo v) {
		UpdateWrapper<MessagePush> updateWrapper = new UpdateWrapper<>();
		updateWrapper.lambda()
			.eq(MessagePush::getReceiver, v.getReceiver())
			.eq(StringUtil.isNotBlank(v.getType()), MessagePush::getType, v.getType())
			.eq(StringUtil.isNotBlank(v.getBizType()), MessagePush::getBizType, v.getBizType())
			.eq(MessagePush::getHasRead, 0)
			.set(MessagePush::getHasRead, 1)
			.set(MessagePush::getReadTime, DateUtil.now());
		return update(updateWrapper);
	}

	/**
	 * 获取用户未读消息数
	 * @param userId 用户id
	 * @param type 消息类型
	 * @param bizType 消息类型
	 * @return
	 */
	@Override
	public Integer queryUnreadMessageCount(Long userId, String type, String bizType) {
		return baseMapper.selectCount(new LambdaQueryWrapper<MessagePush>()
			.eq(MessagePush::getReceiver, userId)
			.eq(StringUtil.isNotBlank(type), MessagePush::getType, type)
			.eq(StringUtil.isNotBlank(bizType), MessagePush::getBizType, bizType)
			.eq(MessagePush::getHasRead, 0)
			.eq(BaseEntity::getIsDeleted, 0));
	}

}
